vipServer=************:43862
grayServer=*************:11163
stageServer=*************:14234
urgentServer=************:18246
yinluServer=************:53014
haoliyouServer=************:44248
yqslServer=************:55431
jacocoServer=*************:10563
meifuServer=************:15965

#apibus的路由,灰度企业则走foneshare-gray(fs-paas-auth-gray)
jacocoTenantIds=756238,662973,663051

grayTenantIds=810633
stageTenantIds=
urgentTenantIds=

vipTenantIds=571465,705221,663598,685571,702081,598335,457482,417440,701872,615153,322246,700256,650993,560528,642028,488989,49148,684811,687979,7689,589227,672394,565556,678719,706169,701575,690045,684567,707365,682928,679182,680306,674454,684142,565179,585503,564761,472252,667973,700347,307447,689107,684738,619318,601362,675169,549366,381492,633975,570967,611667,568645,670625,455014,606457,632829,635329,542661,481495,498044,99598,199529,526380,86506,570814,627115,489207,597902,90706,678825,633761,85421,397315,389877,606802,595995,577382,642824,636965,585375,624115,496991,562487,542956,564174,631171,642294,597846,511164,597566,701560,604891,111024,78722,649328,77617,339525,615315,678793,57515,61456,331894,59472,55002,507719,256184,448142,102177,419804,701878,35187,680515,32099,6030,127914,17824,632477,656230,658399,632768,577152,629927,488995,580984,648162,324519,541219,678424,424059,82897,491187,660291,674400,619615,629257,592135,603207,617168,671597,588110,642812,210294,556015,673842,704320,702766,705239,701899,702767,689024,682722,680935,683002,678447,677184,623264,678845,671260,678853,535673,635758,622836,200501,646222,636781,664226,626246,324450,461402,595453,662757,503575,12,581718,594192,598144,643224,670624,723801,571465,297351,667707,542661,749926,731593,718821,748879,729737,727748,701872,666335,577382,585375,615153,766920,60000097,683209,729850,736581,640242,615315,700146,642028,762479,58683,49148,419804,719977,727709,756774,758553,768595,17824,730567,712059,757088,560121,648162,678424,771797,724449,540208,711466,589227,672394,716315,756898,756271,60000558,760171,753006,770352,753832,753485,678719,743621,734853,750883,62001627,735924,734282,730723,736142,724803,730707,746614,739798,725446,715311,726368,714776,710159,721871,725906,726805,726675,732567,755850,689024,688901,682928,683418,721369,757079,720547,726694,585503,758091,480907,667973,701884,307447,626246,684738,669412,687779,756589,706221,757485,619318,601362,804230



##字段功能权限灰度名单
fieldPrivilegeGrayTenantId=*

## 查看是否开启订货单的url
 
orderProductUrl=http://************:32339/API/v1/inner/object/delivery_note/service/is_delivery_note_enable

##业务功能权限导出第二版
exportV2=false


## 功能权限分页最大数值
authMaxLimitCount=1000

##有模板发送权限项的对象
sendAsTemplateApiNames=WechatFanObj,EnterpriseInfoObj,MarketingActivityObj,MarketingBehaviorObj,AvailableRangeObj,AvailableAccountObj,AvailableProductObj,AvailablePriceBookObj,CompetitiveLinesObj,CompetitorObj,CompetitiveProductsObj,QuoteLinesObj,QuoteObj,SupplierObj,LeadsObj,PartnerObj,AccountObj,AccountAddrObj,AccountFinInfoObj,ContactObj,MarketingEventObj,ProductObj,SPUObj,SpecificationObj,SpecificationValueObj,NewOpportunityObj,NewOpportunityLinesObj,NewOpportunityContactsObj,SalesOrderObj,InvoiceApplicationObj,InvoiceApplicationLinesObj,PersonnelObj,ContractObj,PriceBookObj,PriceBookProductObj,ReturnedGoodsInvoiceObj,RefundObj,PaymentObj,OrderPaymentObj,PaymentPlanObj,ChargeApproveObj,BehaviorRecordObj
##有发送附件权限项的对象
sendAttachmentApiNames=WechatFanObj,EnterpriseInfoObj,MarketingActivityObj,MarketingBehaviorObj,AvailableRangeObj,AvailableAccountObj,AvailableProductObj,AvailablePriceBookObj,CompetitiveLinesObj,CompetitorObj,CompetitiveProductsObj,QuoteLinesObj,QuoteObj,SupplierObj,LeadsObj,PartnerObj,AccountObj,AccountAddrObj,AccountFinInfoObj,ContactObj,MarketingEventObj,ProductObj,SPUObj,SpecificationObj,SpecificationValueObj,NewOpportunityObj,NewOpportunityLinesObj,NewOpportunityContactsObj,SalesOrderObj,InvoiceApplicationObj,InvoiceApplicationLinesObj,PersonnelObj,ContractObj,PriceBookObj,PriceBookProductObj,ReturnedGoodsInvoiceObj,RefundObj,PaymentObj,OrderPaymentObj,PaymentPlanObj,ChargeApproveObj,BehaviorRecordObj
weChatLicenseList=wechat_standard_100_app,wechat_standard_50_app,wechat_standard_30_app,wechat_strengthen_edition,wechat_standard_edition,wechat_standardpro_upgrade_strengthen_edition,wechat_standard_upgrade_strengthen_edition,wechat_scrm_private_500_industry,wechat_scrm_private_100_industry,wechat_scrm_private_500plus_industry,wechat_scrm_private_30_industry,wechat_scrm_private_200_industry
##企业微信增加两个按钮灰度企业名单
weChatFuncGrayEIs=*

##view edit 权限灰度企业名单
viewEditAllGrayEIs=*

##查看全部对象名单
viewAllApiNames= TPMDealerActivityCostObj,ProductCategoryObj,TieredPriceBookRuleObj,DependenciesObj,CostAdjustmentNoteProductObj,SPUObj,ElectronicSignObj,CoinAccountDetailObj,DeliveryNoteProductObj,TPMActivityProofObj,DistributorSupplyObj,TPMActivityBudgetObj,CheckinsVerifyObj,OutboundDeliveryNoteProductObj,EmployeeLoginUsageObj,TPMDealerActivityObj,TPMActivityAgreementDetailObj,TimeSheetObj,WechatAccountGroupStatisticsObj,DealerSupplyObj,MarketingActivityObj,ScheduleObj,BlogObj,BOMObj,AmortizeInfoObj,LedgerObj,MatchNoteObj,ProductConstraintLinesObj,ServiceLogObj,ScheduleRepeatObj,MustDistributeProductsObj,PaymentPlanObj,StockReportingObj,ProjectStageObj,TPMActivityProofAuditDetailObj,RebatePolicyObj,SpecificationObj,PolicyOccupyObj,PriceBookObj,ChargeApproveObj,AccountsReceivableNoteObj,ShoppingCartSummaryObj,AccountObj,StockCheckNoteProductObj,PricePolicyRuleObj,LedgerDetailObj,PricePolicyAccountObj,ServiceFaultCategoryObj,BomAttributeConstraintLinesObj,SupplierObj,ShiftsObj,ProjectObj,CheckinsImgObj,SuccessfulStoreRangeObj,AccountFrozenRecordObj,WechatFriendsRecordObj,RefundObj,CheckinsVerifyDetailObj,WechatAccountStatisticsObj,CheckinsObj,AccountTransactionFlowObj,ShelfReportObj,EmployeePromoteDetailObj,CompetitorObj,WarehouseObj,SparePartsApplicationObj,LandingPageObj,MarketingEventObj,ProcurementAccountObj,FundAccountObj,PurchaseReportingObj,PromotionActivityObj,TelephoneVisitObj,OutboundDeliveryNoteObj,StageTaskObj,CampaignMembersObj,ForecastSumObj,CostAdjustmentNoteObj,NewAdvertisementObj,StockCheckNoteObj,ForecastTaskDetailObj,SaleContractLineObj,NewOpportunityObj,AvailableAccountObj,EmployeeObjectUsageObj,EnterpriseInfoObj,WechatWorkExternalUserObj,DeviceComponentsObj,DealerOutboundDeliveryNoteProductObj,PublicEmployeeObj,PromoterObj,ProjectStandardsObj,QuoteObj,ProjectTaskObj,CompetitiveProductsObj,LeadsFlowRecordObj,TPMActivityProofAuditObj,PricePolicyExcludeAccountObj,AnnounceObj,NewCustomerAccountObj,RequisitionNoteProductObj,AIProductReportMainObj,work_report_obj,SalesInvoiceObj,ForecastRecordObj,TPMActivityItemObj,PointsRewardDetailObj,SupplyStoreObj,TPMActivityProofDetailObj,ServiceKnowledgeObj,ReturnedGoodsInvoiceObj,ActiveRecordObj,WechatFanObj,PaymentOrderObj,ForecastTaskObj,WechatGroupUserObj,MemberObj,ChannelObj,RFMAssessResultObj,TPMActivityObj,CouponInstanceObj,CompetitiveLinesObj,FAccountAuthorizationObj,PriceBookAccountObj,SalesOrderObj,ContractObj,OpportunityObj,ProjectDocumentObj,AvailableRangeObj,MarketingAttributionObj,TermServingLinesObj,PaymentObj,CouponPlanObj,RebateObj,StageInstanceObj,MarketingBehaviorObj,TPMActivityStoreObj,EnterpriseRelationObj,UserVisitObj,MarketingEventInfluenceObj,TieredPriceBookObj,ProductConstraintObj,KeywordServingPlanObj,AvailablePriceBookObj,PricePolicyObj,RebateDetailObj,InvoiceApplicationObj,VisitRouteObj,LandingPageVisitorDetailObj,MarketingKeywordObj,CheckinsImgDetailObj,WechatGroupObj,RebateRuleObj,ContentPropagationDetailObj,ProcurementRuleObj,BehaviorRecordObj,TPMActivityDetailObj,PersonnelObj,RequisitionNoteObj,ProductObj,TPMActivityAgreementObj,TieredPriceBookProductObj,ForecastRuleObj,BomAttributeConstraintObj,LeadsObj,ProcurementAnalysisObj,AccountCheckRuleObj,DealerOutboundDeliveryNoteObj,InvoiceApplicationLinesObj,ContactObj,PricePolicyProductObj,GoalValueObj,JournalObj,CoinAccountObj,CasesObj,SaleContractObj,RFMRuleObj,SpecialSupplyObj,CompetitiveProductReportingObj,AvailableProductObj

##编辑全部对象名单
editAllApiNames =TPMDealerActivityCostObj,ProductCategoryObj,TieredPriceBookRuleObj,DependenciesObj,CostAdjustmentNoteProductObj,SPUObj,CoinAccountDetailObj,DeliveryNoteProductObj,TPMActivityProofObj,DistributorSupplyObj,TPMActivityBudgetObj,CheckinsVerifyObj,OutboundDeliveryNoteProductObj,TPMDealerActivityObj,TimeSheetObj,TPMActivityAgreementDetailObj,WechatAccountGroupStatisticsObj,DealerSupplyObj,BlogObj,ScheduleObj,BOMObj,AmortizeInfoObj,LedgerObj,ProductConstraintLinesObj,ScheduleRepeatObj,MustDistributeProductsObj,PaymentPlanObj,StockReportingObj,ProjectStageObj,TPMActivityProofAuditDetailObj,SpecificationObj,PolicyOccupyObj,PriceBookObj,AccountsReceivableNoteObj,ChargeApproveObj,ShoppingCartSummaryObj,AccountObj,StockCheckNoteProductObj,PricePolicyRuleObj,LedgerDetailObj,PricePolicyAccountObj,ServiceFaultCategoryObj,BomAttributeConstraintLinesObj,SupplierObj,ProjectObj,ShiftsObj,CheckinsImgObj,SuccessfulStoreRangeObj,WechatFriendsRecordObj,RefundObj,CheckinsVerifyDetailObj,WechatAccountStatisticsObj,AccountTransactionFlowObj,CheckinsObj,ShelfReportObj,EmployeePromoteDetailObj,CompetitorObj,WarehouseObj,SparePartsApplicationObj,LandingPageObj,MarketingEventObj,FundAccountObj,PromotionActivityObj,PurchaseReportingObj,TelephoneVisitObj,OutboundDeliveryNoteObj,StageTaskObj,CampaignMembersObj,CostAdjustmentNoteObj,NewAdvertisementObj,StockCheckNoteObj,ForecastTaskDetailObj,SaleContractLineObj,AvailableAccountObj,NewOpportunityObj,EnterpriseInfoObj,WechatWorkExternalUserObj,DealerOutboundDeliveryNoteProductObj,DeviceComponentsObj,PublicEmployeeObj,ProjectStandardsObj,PromoterObj,ProjectTaskObj,QuoteObj,CompetitiveProductsObj,LeadsFlowRecordObj,TPMActivityProofAuditObj,PricePolicyExcludeAccountObj,AnnounceObj,RequisitionNoteProductObj,AIProductReportMainObj,SalesInvoiceObj,ForecastRecordObj,TPMActivityItemObj,PointsRewardDetailObj,SupplyStoreObj,TPMActivityProofDetailObj,ServiceKnowledgeObj,ActiveRecordObj,ReturnedGoodsInvoiceObj,PaymentOrderObj,ChannelObj,RFMAssessResultObj,CouponInstanceObj,TPMActivityObj,CompetitiveLinesObj,FAccountAuthorizationObj,PriceBookAccountObj,SalesOrderObj,ContractObj,OpportunityObj,ProjectDocumentObj,AvailableRangeObj,MarketingAttributionObj,TermServingLinesObj,PaymentObj,CouponPlanObj,RebateObj,StageInstanceObj,MarketingBehaviorObj,EnterpriseRelationObj,TPMActivityStoreObj,MarketingEventInfluenceObj,KeywordServingPlanObj,ProductConstraintObj,TieredPriceBookObj,AvailablePriceBookObj,PricePolicyObj,RebateDetailObj,InvoiceApplicationObj,VisitRouteObj,LandingPageVisitorDetailObj,MarketingKeywordObj,CheckinsImgDetailObj,ContentPropagationDetailObj,RebateRuleObj,BehaviorRecordObj,ProcurementRuleObj,TPMActivityDetailObj,RequisitionNoteObj,ProductObj,TPMActivityAgreementObj,TieredPriceBookProductObj,BomAttributeConstraintObj,ForecastRuleObj,LeadsObj,AccountCheckRuleObj,DealerOutboundDeliveryNoteObj,InvoiceApplicationLinesObj,ContactObj,PricePolicyProductObj,GoalValueObj,JournalObj,CoinAccountObj,CasesObj,RFMRuleObj,SaleContractObj,CompetitiveProductReportingObj,SpecialSupplyObj,AvailableProductObj

##租户库查看全部预设对象
viewAllTenantApiNames=ExchangeGoodsNoteProductObj,ProjectStandards,NonstandardAttributeObj,CustomerServiceSessionObj,SevicePlanDetailObj,ProductBrandObj,LeaveMessageObj,MarketingFeedsObj,ConsultFormObj,ServiceRequestObj,AdDataReturnDetailObj,SuccessfulStoreRange,DealerReturnOrderProductObj,CustomObj,CustomerCategoryObj,CasesCheckinsSceneObj,ServiceAreaDetailObj,RefundMaterialBillProductObj,InternalSignCertifyObj,LogisticsInfoObj,DevicePlanObj,DemoxMeetingRoomEventObj,MarketingProcessLatencyResultObj,MarketingSpreadChannelObj,GoodsReceivedNoteObj,MemberGradeObj,WechatCouponObj,CasesServicePersonnelObj,CasesCheckGroupObj,AccessoryExchangeObj,ChannelNameObj,AttributePriceBookObj,AccountCategoryObj,DeviceObj,ProductCollectionObj,TPMBudgetAccountObj,SOPProcedureInstanceObj,DemoxMeetingRoomAreaObj,BpmInstance,AIMainObj,MemberGrowthValueDetailObj,TPMBudgetStatisticTableObj,StoreSalesVolumeProductObj,DealerOrderObj,TPMBudgetTransferDetailObj,GoodsReceivedNoteProductObj,SOPActionTemplateObj,TPMBudgetDisassemblyObj,EngineerInfoObj,LeadsTransferLogObj,BatchStockObj,SevicePlanObj,TPMBudgetOccupationDetailObj,SalesmanStatementObj,AreaManageObj,ReceiveMaterialBillProductObj,AggregateRuleObj,CustomWarehouseObj,DealerOrderProductObj,ServiceAreaObj,TPMActivityBudgetDetailObj,IndustryPriceBookObj,DealerPointsRewardDetailObj,PromotionProductObj,PurchaseOrderProductObj,TelesalesRecordObj,ServiceReportObj,BomInstanceObj,MemberGradeEquitiesRuleObj,StatementObj,NecessarySkillObj,DeliveryNoteObj,DealerWarehouseObj,InventoryDetailsObj,TPMBudgetAccountDetailObj,PreventiveMaintenanceObj,PurchaseReturnNoteObj,AttachObj,AccountMainDataObj,CheckGroupObj,CustomerAccountObj,ReceiveMaterialBillObj,CustomerReceivedNoteObj,SalesScopeObj,DeviceAccessoryChangeRecordObj,RebatePolicyLogObj,PrepayDetailObj,StoreStockObj,PartnerReportObj,DevicePlanDetailObj,CasesAccessoryUseInfoObj,EmployeeWarehouseDetailObj,StoreReceivedNoteObj,CasesCheckinsObj,WechatConversionObj,SOPInstanceObj,CoveredProductObj,RebateUseRuleObj,MemberEquitiesObj,DealerReturnOrderObj,PurchaseReturnNoteProductObj,AccountSignCertifyObj,DealerGoodsReceivedNoteProductObj,AdvertisementObj,SignRecordObj,DevicePartObj,MailObj,StoreSalesVolumeObj,EmployeeWarehouseObj,FeeSettlementBillObj,ServiceRecordObj,TPMBudgetAccrualObj,SOPActionInstanceObj,ReturnNoticeNoteProductObj,ServiceProjectObj,UserCouponObj,AppraiseObj,ServiceSkillObj,StockDetailsObj,StockObj,AttributeApplicablePriceBookObj,AggregateValueObj,MktContentMgmtLogObj,IndustryPriceBookProductObj,TPMActivityItemCostStandardObj,TPMStoreWriteOffObj,MemberIntegralDetailObj,PointsGoodsObj,UserMarketingAccountObj,FundReturnBackObj,SLAResultObj,CustomerReceivedNoteProductObj,RebateIncomeDetailObj,DemoxMeetingRoomObj,DealerStockObj,SalesScopeProductObj,ErpOrganizationObj,DealerDeliveryNoteObj,DealerReturnApplicationObj,SkillLevelModelObj,CasesDeviceObj,ErpWarehouseObj,PreventiveMaintenanceDetailObj,DealerReturnNoticeNoteProductObj,SerialNumberObj,DealerGoodsReceivedNoteObj,SatisfactionRecordObj,PointsExchangeRecordObj,ExchangeReturnNoteObj,TPMBudgetBusinessSubjectObj,PurchaseOrderObj,CheckGroupItemObj,EmployeeWarehouseInOutRecordObj,DealerDeliveryNoteProductObj,AccessoryExchangeDetailObj,AssetInformationObj,TPMBudgetAccrualDetailObj,TPMActivityBudgetAdjustObj,WebImVisitorObj,TPMBudgetCarryForwardObj,DealerReturnNoticeNoteObj,RebatePolicyRuleObj,BatchObj,SOPProcedureTemplateObj,DealerCheckinsObj,DealerReturnProductObj,SOPTemplateObj,SparePartsConsumptionObj,PromotionObj,StoreReceivedNoteProductObj,CommonUnitObj,ErpStockObj,ApprovalInstanceObj,ExchangeReturnNoteProductObj,PricePolicyLimitAccountObj,CheckGroupItemOptionObj,ReturnNoticeNoteObj,TPMBudgetCalculateObj,CasesServiceProjectObj,PartnerObj,UnitInfoObj,CouponObj,InteractiveRecordObj,AttributeObj,AttributePriceBookLinesObj,EmployeeWarehouseAdjustmentNoteObj,RebateOutcomeDetailObj,MarketingProcessObj,ReimbursedApproveObj,CheckRecordObj,ExchangeGoodsNoteObj,RefundMaterialBillObj,CasesCheckGroupItemObj,TPMActivityUnifiedCaseObj,KnowledgeAttachmentRecordObj,ConsultQuestionRecordObj,RemoteServiceRecordObj,CustomerServiceStatusChangeRecordObj,FeeDetailObj

##租户库编辑全部预设对象
editAllTenantApiNames=ExchangeGoodsNoteProductObj,MemberEquitiesObj,DealerReturnOrderObj,NonstandardAttributeObj,CustomerServiceSessionObj,PurchaseReturnNoteProductObj,AccountSignCertifyObj,DealerGoodsReceivedNoteProductObj,SevicePlanDetailObj,ProductBrandObj,AdvertisementObj,LeaveMessageObj,MarketingFeedsObj,ConsultFormObj,DevicePartObj,MailObj,ServiceRequestObj,AdDataReturnDetailObj,StoreSalesVolumeObj,EmployeeWarehouseObj,DealerReturnOrderProductObj,FeeSettlementBillObj,MarketingActivityObj,CustomObj,CustomerCategoryObj,CasesCheckinsSceneObj,ServiceAreaDetailObj,TPMBudgetAccrualObj,SOPActionInstanceObj,ReturnNoticeNoteProductObj,RefundMaterialBillProductObj,InternalSignCertifyObj,ServiceProjectObj,LogisticsInfoObj,UserCouponObj,AppraiseObj,DevicePlanObj,RebatePolicyObj,ServiceSkillObj,DemoxMeetingRoomEventObj,MarketingProcessLatencyResultObj,MarketingSpreadChannelObj,GoodsReceivedNoteObj,MemberGradeObj,WechatCouponObj,CasesServicePersonnelObj,StockObj,AttributeApplicablePriceBookObj,CasesCheckGroupObj,AggregateValueObj,MktContentMgmtLogObj,AccessoryExchangeObj,IndustryPriceBookProductObj,TPMActivityItemCostStandardObj,TPMStoreWriteOffObj,MemberIntegralDetailObj,PointsGoodsObj,ChannelNameObj,UserMarketingAccountObj,AttributePriceBookObj,AccountCategoryObj,DeviceObj,FundReturnBackObj,SLAResultObj,ProductCollectionObj,TPMBudgetAccountObj,CustomerReceivedNoteProductObj,SOPProcedureInstanceObj,DemoxMeetingRoomAreaObj,AIMainObj,MemberGrowthValueDetailObj,TPMBudgetStatisticTableObj,RebateIncomeDetailObj,StoreSalesVolumeProductObj,DemoxMeetingRoomObj,DealerStockObj,DealerOrderObj,TPMBudgetTransferDetailObj,GoodsReceivedNoteProductObj,SOPActionTemplateObj,ErpOrganizationObj,TPMBudgetDisassemblyObj,EngineerInfoObj,DealerDeliveryNoteObj,DealerReturnApplicationObj,SkillLevelModelObj,CasesDeviceObj,SevicePlanObj,ForecastSumObj,TPMBudgetOccupationDetailObj,PreventiveMaintenanceDetailObj,DealerReturnNoticeNoteProductObj,SerialNumberObj,DealerGoodsReceivedNoteObj,SatisfactionRecordObj,PointsExchangeRecordObj,ExchangeReturnNoteObj,TPMBudgetBusinessSubjectObj,AreaManageObj,PurchaseOrderObj,ReceiveMaterialBillProductObj,CheckGroupItemObj,EmployeeWarehouseInOutRecordObj,AggregateRuleObj,DealerDeliveryNoteProductObj,AccessoryExchangeDetailObj,CustomWarehouseObj,DealerOrderProductObj,ServiceAreaObj,AssetInformationObj,TPMActivityBudgetDetailObj,TPMBudgetAccrualDetailObj,IndustryPriceBookObj,TPMActivityBudgetAdjustObj,DealerPointsRewardDetailObj,WebImVisitorObj,PromotionProductObj,PurchaseOrderProductObj,TelesalesRecordObj,TPMBudgetCarryForwardObj,DealerReturnNoticeNoteObj,RebatePolicyRuleObj,ServiceReportObj,BatchObj,BomInstanceObj,MemberGradeEquitiesRuleObj,SOPProcedureTemplateObj,StatementObj,DealerCheckinsObj,WechatFanObj,NecessarySkillObj,DeliveryNoteObj,DealerWarehouseObj,WechatGroupUserObj,DealerReturnProductObj,MemberObj,InventoryDetailsObj,TPMBudgetAccountDetailObj,PreventiveMaintenanceObj,PurchaseReturnNoteObj,SOPTemplateObj,AttachObj,SparePartsConsumptionObj,AccountMainDataObj,CheckGroupObj,PromotionObj,CustomerAccountObj,StoreReceivedNoteProductObj,ReceiveMaterialBillObj,CommonUnitObj,CustomerReceivedNoteObj,SalesScopeObj,DeviceAccessoryChangeRecordObj,UserVisitObj,ApprovalInstanceObj,ExchangeReturnNoteProductObj,PricePolicyLimitAccountObj,RebatePolicyLogObj,CheckGroupItemOptionObj,PrepayDetailObj,ReturnNoticeNoteObj,WechatGroupObj,StoreStockObj,TPMBudgetCalculateObj,CasesServiceProjectObj,PartnerReportObj,DevicePlanDetailObj,PartnerObj,UnitInfoObj,CouponObj,CasesAccessoryUseInfoObj,EmployeeWarehouseDetailObj,AttributeObj,AttributePriceBookLinesObj,EmployeeWarehouseAdjustmentNoteObj,MarketingProcessObj,StoreReceivedNoteObj,CasesCheckinsObj,ReimbursedApproveObj,CheckRecordObj,SOPInstanceObj,CoveredProductObj,RebateUseRuleObj,ExchangeGoodsNoteObj,RefundMaterialBillObj,CasesCheckGroupItemObj,TPMActivityUnifiedCaseObj,KnowledgeAttachmentRecordObj,ConsultQuestionRecordObj,RemoteServiceRecordObj,CustomerServiceStatusChangeRecordObj,FeeDetailObj,ServiceRecordObj

##查看编辑全部是否处于刷库阶段
viewEditGray=false

##绑定权限的CRM角色
viewEditCRMRoles=00000000000000000000000000000006

##绑定权限的管理角色
viewEditSystemRoles=31

##是否只处理自定义对象，全网的时候修改为true
onlyCustom=false

## 判断是否开启企微绑定的rest接口，直接访问到纷享云
## fsEaToOutEaUrl=http://************:48826/open/qyweixin/restProxy/fsEaToOutEa

## 判断是否开启企微绑定的rest接口，直接访问到纷享云
fsEaToOutEaUrl=http://************:48826/open/qyweixin/restProxy/fsEaToOutEa

##业务角色列表根据分管小组进行过滤
getBusinessRoleListByManageGroupUrl=http://************:39414/fs-plat-service-biz/Management/ManageGroups/getObjListByManageGroup



##创建业务角色时将当前角色分配到小组中
addObjToManageGroupByUserIdUrl=http://************:39414/fs-plat-service-biz/Management/ManageGroups/innerAddObjToManageGroupByUserId

##新增功能权限缓存，新的灰度变量
userFuncCheckV2=*


openWeChat=true

##刷库工具通过专属云找到指定云企业进行apiBus路由
##1纷享 2双胞胎 3紫光生产 4金山 5华为 6紫光测试 8阿里 9法兰克福 10模板 11海信 12许继 13复制 14铁塔 15蒙牛 16何氏眼科 17扬农化工 18伍子醉 19海克斯康 20科大讯飞 21统信
transfer_sysDBEnvTenantIdMap={"1":"663123","2":"701708","3":"700684","4":"705139","5":"60000053","6":"590158","8":"62001494","9":"670806","10":"757917","11":"761775","12":"40030009","13":"40030007","14":"40070006","15":"40163217","16":"760825","17":"40030082","18":"40030079","19":"40030081","20":"40030078","21":"40030101","22":"40030118","23":"40030119","24":"40030133","25":"40030135","26":"40030137","27":"40030158","29":"40030169","7":"40030174"}

getOrderProductUrl=http://************:30393/API/v1/inner/rest/object_describe

grayExportTenantIds=*

openNewAopLog=true

##从对象对应的主对象，从对象走主对象的功能权限
slaveWithMaster={"SalesOrderProductObj":"SalesOrderObj","CasesServiceProjectObj":"CasesObj","CasesDeviceObj":"CasesObj","NecessarySkillObj":"CasesObj","ServiceRequestLinesObj":"ServiceRequestObj","ServiceAgreementDetailObj":"ServiceAgreementObj","ServiceFaultDetailObj":"ServiceFaultObj","DevicePlanDetailObj":"DevicePlanObj","SkillLevelModelDetailObj":"SkillLevelModelObj","PreventiveMaintenanceDetailObj":"PreventiveMaintenanceObj","FeeSettlementDetailObj":"FeeSettlementBillObj","ServiceAreaDetailObj":"ServiceAreaObj","EngineerAreaObj":"EngineerInfoObj","EmployeeSkillObj":"EngineerInfoObj"}


getUserLicenseListUrl=http://fs-paas-license.nsvc.foneshare.cn/fs-paas-license/paas/license/user/license

##功能权限服务刷库时不清除缓存
isNeedCleanCache=false


