service-locator=http://************:33206/app/server/list/?address=
NAMESRV_ADDR=***********:9876;***********:9876
cmdb.url=http://cmdb.foneshare.cn/index.php?r=biz-oss/get-all
cas.uriFindUserById=/find/byId
cas.uriFindUsersByName=/find/byName
cas.serverUserCenter=http://oss.foneshare.cn/user
report-pv-enabled=true
report-pv-limit=0
rpc.slowThreshold=5000
http.minTimeout=3000
uri-black-list=/H/V5Messenger/checkUpdatedAsync,/H/GlobalInfo/GetRemindGlobalInfo,/FHH/EMXHBaichuan/Messenger/CheckUpdated,/FED/A/Messenger/Download/PULL_START,/FED/A/Messenger/Download/PULL_DATA,/FED/A/Messenger/Download/PULL_COMPLETE,/FED/A/Messenger/UpdateSessionStatus/QUERY,/FED/A/Compatible/UploadTempFile/FILE_DATA,/FED/A/Compatible/UploadTempFile/FILE_START,/FED/A/Messenger/GetSessionList/QUERY,/FED/A/Messenger/GetUniversalSessionDefinitions/QUERY,/FED/A/Messenger/GetUniversalSessionDefinitions/QUERY,/FED/EM1AORGBIZ/Organization/getByVersion/QUERY,/FHH/EM1HWQREST/deferred/getDataScreen,/FHH/EM1HWQREST/deferred/getBusinessDataScreen
#jaeger-agent地址 ip:port 和包最大限制字节数
jaeger.sender.server=***********
jaeger.sender.port=6831
jaeger.sender.maxPacketSize=65000
#上报策略: 间隔时间 5s, 队列长度 1024
jaeger.reporter.flushInterval=5000
jaeger.reporter.maxQueueSize=1024
#开启jaeger trace
jaeger.trace.open=false
#每20秒访问次数超过这个阈值就上报es
report-sql-stat-limit=20
localLogPolicy=paas-db-operator:DISCARD,metadata-console-compose:WARN,fs-sync-data-all:WARN,open-api-gateway-web:WARN,fs-paas-refresh-forest:WARN,fs-paas-app-udobj-rest:WARN