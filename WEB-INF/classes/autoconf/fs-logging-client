hosts=************:4560,************:4560
countLimit=100000
starting = false

log-level = 20000
exclude-logger-names = com.fxiaoke.metrics,com.fxiaoke.log,com.fxiaoke.logconf.servlets.LogbackServlet,RocketmqClient,com.fxiaoke.support.KafkaSender,ACCESS,Access,c.f.o.p.a.ProviderPerformanceLog,c.f.h.i.m.d.HubbleESDocRefreshEventListener,c.fxiaoke.es.service.DataService,o.e.client.RestClient
include-logger-names = com.fxiaoke,com.facishare

# loki config start
loki-open=false
loki-entries-batch-size=100
loki-server= ************:62569
loki-router-server=************:45837
loki-client-refresh-seconds=3600
loki-log-threshold-pre-minute=60000
loki-log-threshold-unlimited-apps=fast-notifier
loki-line-max-chart=1048576
loki-log-level=INFO
loki-exclude-logger-names=com.fxiaoke.metrics,com.fxiaoke.log,com.fxiaoke.logconf.servlets.LogbackServlet,RocketmqClient,com.fxiaoke.support.KafkaSender
#loki config end

app-log-open=true
app-log-threshold-pre-minute=10000
app-log-threshold-unlimited-apps=fast-notifier
app-log-use-rowBinary-apps=*
app-log-level=INFO
app-log-exclude-logger-names=com.fxiaoke.metrics,com.fxiaoke.log,com.fxiaoke.logconf.servlets.LogbackServlet,RocketmqClient,com.fxiaoke.support.KafkaSender

mask-sensitive=true
sensitive-words=accesskey,accessKey,ak,appkey,appKey,appsecret,appSecret,authXC,card,chineseContent,content,cookie,Cookie,cookies,Cookies,email,FSAuthXC,idcard,idCard,key,mail,messageContent,passwd,password,phone,pwd,PWD,secretkey,secretKey,sig,sk,tel,telephone,token,value,Value,保密,密码,密钥,帐号,帐户,手机,护照,护照号,支付,敏感,用户名,电话,绝密,证书,账号,账户,账户,身份证,身份证号,车牌,车牌号,邮箱,银行,银行帐号,银行帐户,银行账户,驾驶证,驾驶证号