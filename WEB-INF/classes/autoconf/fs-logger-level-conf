[fs-plat-org-adapter-provider]
scope=foneshare-gray
com.facishare.organization.adapter=DEBUG

[fs-cep-provider]
scope=jacoco
com.github.autoconf.base.ChangeableConfig=INFO

[datax-sync]
#scope=foneshare,foneshare-vip,ucd-public-prod,mengniu-public-prod
scope=jingbo-public-prod
com.fxiaoke.datax.service=DEBUG
com.fxiaoke.datax.processor.OpLogMessageProcessor=INFO
com.fxiaoke.datax.writer.JdbcWriterServiceImpl=WARN

[fs-mq-bus]
com.facishare.mq.bus.core.RedirectMessageService=OFF

[fs-bi-sqlengine]
com.facishare.bi.sqlengine=INFO

[weex-console-service]
scope=foneshare-gray
com.fxiaoke.weex.controller=INFO


[i18n-service]
scope=mengniu-public-prod
com.fxiaoke.i18n=INFO

[paas-datax-pusher]
scope=foneshare,foneshare-vip
com.fxiaoke.datax.parser=INFO
com.fxiaoke.datax.service=INFO
com.fxiaoke.datax.cache=WARN
com.fxiaoke.datax.util=INFO


[weex-console-service]
scope=foneshare
com.fxiaoke.weex.service=INFO
com.fxiaoke.weex.controller=INFO


[fs-paas-calculate-task]
scope=xjgc-public-prod,foneshare-stage
com.facishare.paas.task.calculate.service.SendMessageService=INFO
com.facishare.paas.task.calculate=INFO
com.fxiaoke.i18n.client.impl.LocalCache=INFO

[fs-paas-app-udobj]
scope=foneshare-stage
com.fxiaoke.i18n.client.impl.LocalCache=INFO

[fs-paas-function-service-runtime-provider]
scope=foneshare-ruijie 
com.facishare.function.aop.PerformanceLogger=ERROR

[fs-paas-auth-provider]
scope=forceecrm-public-prod
org.springframework=DEBUG

[fs-paas-function-service-debug]
scope=foneshare-svip
com.fxiaoke.common.http.spring.HttpSupportFactoryBean=DEBUG

[fs-training]
scope=foneshare
com.fxiaoke=INFO
com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer=INFO

[pg-scanner-ui]
scope=foneshare
com.fxiaoke.paas.service.PaasDataService=DEBUG
com.fxiaoke.paas.service.PgStatService=DEBUG
com.fxiaoke.paas.web.PaasDataApiController=DEBUG
com.fxiaoke.paas.web.PgStatApiController=DEBUG

[fs-sync-data-all]
scope=mengniu-public-prod
com.fxiaoke.dispatcher.processor.LockService=INFO
com.fxiaoke.dispatcher.processor.Dispatcher=INFO

[fs-file-server]
scope=foneshare
com.fxiaoke.file.server.web.AccessFileController=INFO

[data-auth-worker]
scope=xjgc-public-prod
com.fxiaoke.dispatcher.processor=DEBUG

[fs-eservice-mq-listener]
scope=foneshare
com.facishare.eservice.mq.parser.MetaDataMessageParser=INFO
com.facishare.eservice.mq.processor.MetaDataMessageProcessor=INFO

[fs-erp-sync-data]
com.fxiaoke.dispatcher=INFO


[paas-bi-copier]
scope=foneshare-gray
com.fxiaoke.dispatcher=INFO

