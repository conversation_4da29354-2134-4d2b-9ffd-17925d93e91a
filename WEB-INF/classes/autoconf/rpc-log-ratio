_default_=0
EnterpriseDataService=1
MessengerController=1
AccountService=1
SessionAction=1
redis:qixin_redis_status_remote=1
redis:fs-qixin-redis-status-remote=1
ActiveSessionService=1
UCGraySwitchService=1
RemotePropertiesService=1
OpenFsUserAppViewService=1
EmployeeEditionService=1
shardRedis:fs-uc-redis-entity=1
UserCenterTryService=1
EnterpriseEntityService=1
EmployeeEntityService=1
fs-warehouse-metadata-mongo=0
fs-warehouse-enterprise-metadata-mongo=0
fs-warehouse-metadata-mongo-gc=0
shardRedis:fs-warehouse-metadata-redis=0
shardRedis:fs-warehouse-file-redis=0
WarehouseFastDFSClient=0
NFileStorageServiceImpl=0
FileDataRepositoriesImpl=0
MetaDataRepositoriesImpl=0
FileDataCacheImpl=1
MetaDataCacheImpl=1
FileDataDaoImpl=1
MetaDataDaoImpl=1
FastDFSGroupManagerImpl=0
WebNotificationService=1
FAPNotificationService=1
OnlineStatusService=1
GrayAccessService=0
PNSTokenService=1
PushEventProcessor=1
OnlineManageService=1
sentinel:fs-oms-redis-enterprise=1
sentinel:fs-oms-redis=1
FcpClientMgr=1
fs-oms-mongo-pnstoken=1
OnlineStatusServiceWrapper=1
