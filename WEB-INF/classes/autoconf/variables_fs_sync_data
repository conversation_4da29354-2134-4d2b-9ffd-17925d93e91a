#聚合框架检测是否有数据，单位s
check_data_interval=5
normal_addr =************:56749
dbserver=172.17.157.203:5432
password=4F365368D241D4FC8C14AB96793025DA82F7ACE92AAD77E22B6E6EFDEB92667D
gray_tenantIds=[665485,741081,663053,539161,712174,720724,727702,727699,727701,757797,755223,663052,736516,761776,751871,721788]
gray_tenantIds_list = 665485,741081,663053,539161,712174,720724,727702,727699,727701,757797,755223,663052
reverse_syncdata_max_times=10
#更改集按钮显示
crmSyncDataChangeSetMenu=,{"children":[{"menuCode":"crm_sync_ploy","model":"crm_sync_ploy","name":"同步对象"},{"menuCode":"crm_sync_ploy_detail","model":"crm_sync_ploy_detail","name":"同步策略"}],"menuCode":"crm_syncdata","name":"数据同步"}
#同步工具
clickhouse_uri = *******************************************
clickhouse_base_url = https://log.foneshare.cn/query/?
clickhouse_user = admin
clickhouse_password = 9B84B52A69E0E1C385D25F3E3E7DC90269A8A63B10ADCC02067AE0601C7DDC5B
app_log_name = logger.app_log_v2
app_log_index = 2
app_log_tid = 104
#设置产品写入接口使用action业务接口
action_gray_tenantId=["791752","791957","795870"]
#mq远程调用地址
baseUrl=http://************:39414/fs-sync-data-all
redis_servers=************:30001;************:30001;************:30001;************:30001;************:30001
redis_masterName=prodredis19
redis_password=Soo0eip6
redis_sentinel=true
redis_dbIndex=0
startup=true
pool_maxActive=30
pool_minIdle=1
pool_maxWaitMillis=3000
pool_testOnBorrow=false
pool_testOnReturn=false
pool_testWhileIdle=true
paas_object_mq_rate_limiter=300
#设置需要根据hash设置topic的企业
dispatch_hashtopic_tenants=["721787","721788","739241","800947"]
#设置hash槽值
dispatch_hashtopic_clusters=8
dispatch_consumer_state=running
dispatch_state=running
dispatch_store_topics_limit=deny:jacoco,jacoco_af,test,test_af,buf_fsdb012091_721788_0,buf_fsdb012091_721788_1,buf_fsdb012091_721788_2,buf_fsdb012091_721788_3,buf_fsdb012091_721788_4,buf_fsdb012091_721788_5,buf_fsdb012091_721788_6,buf_fsdb012091_721788_7,buf_fsdb012017_739241_0,buf_fsdb012017_739241_1,buf_fsdb012017_739241_2,buf_fsdb012017_739241_3,buf_fsdb012017_739241_4,buf_fsdb012017_739241_5,buf_fsdb012017_739241_6,buf_fsdb012017_739241_7,buf_fsdb012091_721787_0,buf_fsdb012091_721787_1,buf_fsdb012091_721787_2,buf_fsdb012091_721787_3,buf_fsdb012091_721787_4,buf_fsdb012091_721787_5,buf_fsdb012091_721787_6,buf_fsdb012091_721787_7,buf_fsdb012091_721788_0_af,buf_fsdb012091_721788_1_af,buf_fsdb012091_721788_2_af,buf_fsdb012091_721788_3_af,buf_fsdb012091_721788_4_af,buf_fsdb012091_721788_5_af,buf_fsdb012091_721788_6_af,buf_fsdb012091_721788_7_af,buf_fsdb012017_739241_0_af,buf_fsdb012017_739241_1_af,buf_fsdb012017_739241_2_af,buf_fsdb012017_739241_3_af,buf_fsdb012017_739241_4_af,buf_fsdb012017_739241_5_af,buf_fsdb012017_739241_6_af,buf_fsdb012017_739241_7_af,buf_fsdb012091_721787_0_af,buf_fsdb012091_721787_1_af,buf_fsdb012091_721787_2_af,buf_fsdb012091_721787_3_af,buf_fsdb012091_721787_4_af,buf_fsdb012091_721787_5_af,buf_fsdb012091_721787_6_af,buf_fsdb012091_721787_7_af
jacoco_tenantIds=["804679","804680","804682","797456","757797","754540"]
# 同步工具-通用脚本工具脚本白名单
script_data_list = [{"id":"1","name":"示例接口1","url":"https://open.fxiaoke.com/cgi/corpAccessToken/get/V2"},{"id":"2","name":"示例接口2","url":"https://**************:34790/cgi/corpAccessToken/get/V2"},{"id":"3","name":"根据数据触发同步","url":"http://************:12875/outrest/syncData/excuteSpeedLimitSyncDataByObjectData"},{"id":"4","name":"通过映射触发限流数据同步(勿动，映射工具也使用此项)","url":"http://************:12875/outrest/syncData/excuteSpeedLimitSyncDataByMapping"}]
second_sync_limit={"default":20.0,"second:buf_fsdb052072:SalesOrderObj":"10"}
first_sync_limit={"first:fsdb052072019:GoodsReceivedNoteObj":15.0,"first:buf_fsdb052072:SalesOrderObj":"10"}
