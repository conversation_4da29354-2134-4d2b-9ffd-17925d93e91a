#jacoco企业
jacoco_crm_ei=789875,796490,720634,797857,760803,759304,760803,797857,776739,758692,785102,785102,758692,759304,817643

# 【紧急】路由到fs-crm-manufacturing-urgent      
urgent_crm_ei=761836,816142,590057,761535,590056,822660,820970,704833,807230,1

# 【gray】 路由到fs-crm-manufacturing-gray   
gray_crm_ei=720136,792852,687779,768595,800649,784665,805642,705448,805186

# 【stage】路由到fs-crm-manufacturing-stage  
stage_crm_ei=776793,807526,822124,804870,807527,822768,780023

# 【VIP】 路由到fs-crm-manufacturing-vip，【注意：从这临时灰度走的企业，灰度完后需要及时迁移回来！！】
# 【VIP企业清单】：721787,735454,719279,726694,612295,741038,735051,701884,57654,678793,615315,724449,548405,760171,457482,705221,746745,327520,701885,748175,58683,726368
# 【银鹭】 正式：721787
# 【元气森林】正式：735454
vip_crm_ei=721787,735454,719279,726694,612295,741038,735051,701884,57654,678793,615315,724449,548405,760171,457482,705221,746745,327520,701885,748175,58683,726368

# 【SSVIP】【锐捷】 路由到fs-crm-manufacturing-ruijie
ruijie_crm_ei=730173

# 【SSVIP】【好丽友】正式：707988     沙盒：734516
haoliyou_crm_ei=707988


# 【SSVIP】【银鹭】 正式：721787
yinlu_crm_ei=
 
 
# 【SSVIP】【元气森林】正式：735454
yqsl_crm_ei=


# fs-crm-manufacturing-rest服务地址 
fs_crm_manufacturing_rest_server=************:16015
 
# fs-crm-manufacturing-rest-01服务地址 
fs_crm_manufacturing_rest_server_01=************:12972
 
# fs-crm-manufacturing-rest-ruijie服务地址 
fs_crm_manufacturing_rest_server_ruijie=************:19836
 
# fs-crm-manufacturing-rest-vip服务地址 
fs_crm_manufacturing_rest_server_vip=************:11231


# fs-crm-manufacturing-vip服务地址  
fs_crm_manufacturing_server_vip=************:39185

# fs-crm-manufacturing-ruijie服务地址  
fs_crm_manufacturing_server_ruijie=************:39277

# fs-crm-manufacturing-haoliyou服务地址  
fs_crm_manufacturing_server_haoliyou=************:64511

# fs-crm-manufacturing-yinlu服务地址 ，950 将流量路由到VIP环境，具体原因：https://wiki.firstshare.cn/pages/viewpage.action?pageId=591036873
# 银鹭环境地址：************:42862  VIP环境地址：************:39185
fs_crm_manufacturing_server_yinlu=************:39185
 
# fs-crm-manufacturing-yqsl服务地址，950 将流量路由到VIP环境，具体原因：https://wiki.firstshare.cn/pages/viewpage.action?pageId=591036873
# 元气环境地址：************:49621  VIP环境地址：************:39185
fs_crm_manufacturing_server_yqsl=************:39185

# fs-crm-manufacturing-urgent服务地址  
fs_crm_manufacturing_server_urgent=************:10011

# fs-crm-manufacturing-stage服务地址  
fs_crm_manufacturing_server_stage=************:11353

# fs-crm-manufacturing-gray服务地址 
fs_crm_manufacturing_server_gray=************:10209

# fs-crm 默认服务地址 fs-crm-manufacturing
# foneshre k8s1 集群
#fs_crm_manufacturing_server_default=************:53594
# foneshre k8s0 集群
fs_crm_manufacturing_server_default=************:17988

# fs-crm jacoco服务地址  
fs_crm_manufacturing_server_jacoco=************:18973

# fs-crm import gray服务地址  
fs_crm_import_manufacturing_server_gray=************:12309

# fs-crm import stage服务地址  
fs_crm_import_manufacturing_server_stage=************:14946

# fs-crm import 默认服务地址   fs-crm-import-manufacturing
fs_crm_import_manufacturing_server_default=************:37053

# crm apibus action、controller路由配置
action_controller_apibus_manufacturing=IndividualStockCountingObj,IndividualStockCountingDetailObj,FundReturnBackObj,ErpStockObj,ErpWarehouseObj,DeliveryNoteProductObj,DeliveryNoteObj,WarehouseObj,StockObj,GoodsReceivedNoteObj,GoodsReceivedNoteProductObj,OutboundDeliveryNoteProductObj,OutboundDeliveryNoteObj,RequisitionNoteProductObj,RequisitionNoteObj,StockCheckNoteProductObj,StockCheckNoteObj,BatchStockObj,BatchObj,ExchangeReturnNoteObj,ExchangeReturnNoteProductObj,SerialNumberObj,SupplierObj,PurchaseOrderObj,PurchaseOrderProductObj,StockDetailsObj,CostAdjustmentNoteObj,CostAdjustmentNoteProductObj,ForecastRecordObj,ForecastSumObj,CasesObj,SLAResultObj,DevicePlanDetailObj,DevicePlanObj,CasesDeviceObj,CasesServiceProjectObj,ServiceProjectObj,SkillLevelModelObj,SkillLevelModelDetailObj,ServiceSkillObj,NecessarySkillObj,EmployeeSkillObj,DeviceComponentsObj,ServiceFaultCategoryObj,ServiceKnowledgeObj,PreventiveMaintenanceObj,PreventiveMaintenanceDetailObj,RefundMaterialBillProductObj,RefundMaterialBillObj,ReceiveMaterialBillProductObj,ReceiveMaterialBillObj,EmployeeWarehouseObj,CasesAccessoryUseInfoObj,AppraiseObj,FeeSettlementDetailObj,FeeSettlementBillObj,FeeDetailObj,CheckRecordObj,DevicePartObj,DeviceObj,AccessoryExchangeObj,CasesCheckinsObj,MailObj,InventoryDetailsObj,DeviceAccessoryChangeRecordObj,PurchaseReturnNoteObj,PurchaseReturnNoteProductObj,DealerReturnApplicationObj,DealerReturnProductObj,ServiceRecordObj,TelesalesRecordObj,EngineerInfoObj,EmployeeWarehouseDetailObj,EmployeeWarehouseInOutRecordObj,AccessoryExchangeDetailObj,SatisfactionRecordObj,ServiceReportObj,LogisticsInfoObj,EmployeeWarehouseAdjustmentNoteObj,CheckGroupObj,CheckGroupItemObj,CheckGroupItemOptionObj,CasesCheckGroupObj,CasesCheckGroupItemObj,SOPProcedureInstanceObj,SOPActionTemplateObj,SOPInstanceObj,SOPActionInstanceObj,SOPProcedureTemplateObj,SOPTemplateObj,SparePartsApplicationDetailObj,SparePartsConsumptionObj,SparePartsConsumptionDetailObj,ServiceRequestObj,ConsultFormObj,SparePartsMaintenancePlanObj,SparePartsMaintenanceTaskObj,SparePartsMaintenanceTaskDetailObj,SparePartsDeliveryObj,SparePartsDeliveryDetailObj,SparePartsReturnObj,SparePartsReturnDetailObj,IndividualStockTransactionsObj,SparePartsApplicationObj,ServiceFaultObj,ServiceFaultDetailObj,ServiceFaultRecordObj,FaultPositionObj,FaultPhenomenonObj,FaultCauseObj,FaultSolutionObj,CasesCheckinsSceneObj,ServiceAgreementUseDetailObj,ServiceAgreementDetailObj,ServiceAgreementObj,KnowledgeParagraphObj,InventoryUnfreezingDetailObj,InventoryFreezingDetailObj,InventoryFreezingObj,ServiceFaultAuditObj,QrcodeObj,DeviceLifeCircleObj,KnowledgeAttachmentRecordObj,ConsultQuestionRecordObj,RemoteServiceRecordObj,AssetBorrowingObj,AssetBorrowingDetailObj,AssetExtensionObj,AssetExtensionDetailObj,AssetReturnObj,AssetReturnDetailObj,AssetTransferObj,AssetTransferDetailObj,CustomerServiceSessionObj,DeviceMeterTypeObj,DeviceMeterObj,DeviceMeterReadingsObj,LogisticsOrderObj,ServiceCenterObj,LogisticsRecordObj,ServiceProgressObj,CustomerServiceStatusChangeRecordObj,WarehouseLocationObj,LocationStockObj,LocationBatchStockObj,ReturnedGoodsInvoiceObj,ReturnedGoodsInvoiceProductObj,DeviceLifeCycleObj,QuestionAnswerPairObj,EServiceGeneralRuleObj,KnowledgeDistributionRuleObj,KnowledgeDistributionRecordObj,KnowledgeArticleObj,KnowledgeSpaceObj,KnowledgeSpaceCategoryObj,KnowledgeIntegrationLogObj,CasesServicePersonnelObj


# crm cep action、controller路由配置
action_controller_cep_manufacturing=IndividualStockCountingObj|IndividualStockCountingDetailObj|FundReturnBackObj|ErpStockObj|ErpWarehouseObj|DeliveryNoteProductObj|DeliveryNoteObj|WarehouseObj|StockObj|GoodsReceivedNoteObj|GoodsReceivedNoteProductObj|OutboundDeliveryNoteProductObj|OutboundDeliveryNoteObj|RequisitionNoteProductObj|RequisitionNoteObj|StockCheckNoteProductObj|StockCheckNoteObj|BatchStockObj|BatchObj|ExchangeReturnNoteObj|ExchangeReturnNoteProductObj|SerialNumberObj|SupplierObj|PurchaseOrderObj|PurchaseOrderProductObj|StockDetailsObj|CostAdjustmentNoteObj|CostAdjustmentNoteProductObj|ForecastRecordObj|ForecastSumObj|CasesObj|SLAResultObj|DevicePlanDetailObj|DevicePlanObj|CasesDeviceObj|CasesServiceProjectObj|ServiceProjectObj|SkillLevelModelObj|SkillLevelModelDetailObj|ServiceSkillObj|NecessarySkillObj|EmployeeSkillObj|DeviceComponentsObj|ServiceFaultCategoryObj|ServiceKnowledgeObj|PreventiveMaintenanceObj|PreventiveMaintenanceDetailObj|RefundMaterialBillProductObj|RefundMaterialBillObj|ReceiveMaterialBillProductObj|ReceiveMaterialBillObj|EmployeeWarehouseObj|CasesAccessoryUseInfoObj|AppraiseObj|FeeSettlementDetailObj|FeeSettlementBillObj|FeeDetailObj|CheckRecordObj|DevicePartObj|DeviceObj|AccessoryExchangeObj|CasesCheckinsObj|MailObj|InventoryDetailsObj|DeviceAccessoryChangeRecordObj|PurchaseReturnNoteObj|PurchaseReturnNoteProductObj|DealerReturnApplicationObj|DealerReturnProductObj|ServiceRecordObj|TelesalesRecordObj|EngineerInfoObj|EmployeeWarehouseDetailObj|EmployeeWarehouseInOutRecordObj|AccessoryExchangeDetailObj|SatisfactionRecordObj|ServiceReportObj|LogisticsInfoObj|EmployeeWarehouseAdjustmentNoteObj|CheckGroupObj|CheckGroupItemObj|CheckGroupItemOptionObj|CasesCheckGroupObj|CasesCheckGroupItemObj|SOPProcedureInstanceObj|SOPActionTemplateObj|SOPInstanceObj|SOPActionInstanceObj|SOPProcedureTemplateObj|SOPTemplateObj|SparePartsApplicationObj|SparePartsApplicationDetailObj|SparePartsConsumptionObj|SparePartsConsumptionDetailObj|ServiceRequestObj|ConsultFormObj|ReturnedGoodsInvoiceObj|ReturnedGoodsInvoiceProductObj|SparePartsMaintenancePlanObj|SparePartsMaintenanceTaskObj|SparePartsMaintenanceTaskDetailObj|SparePartsDeliveryObj|SparePartsDeliveryDetailObj|SparePartsReturnObj|SparePartsReturnDetailObj|IndividualStockTransactionsObj|ServiceFaultObj|ServiceFaultDetailObj|ServiceFaultRecordObj|FaultPositionObj|FaultPhenomenonObj|FaultCauseObj|FaultSolutionObj|CasesCheckinsSceneObj|ServiceAgreementUseDetailObj|ServiceAgreementDetailObj|ServiceAgreementObj|KnowledgeParagraphObj|InventoryUnfreezingDetailObj|InventoryFreezingDetailObj|InventoryFreezingObj|ServiceFaultAuditObj|QrcodeObj|DeviceLifeCircleObj|KnowledgeAttachmentRecordObj|ConsultQuestionRecordObj|RemoteServiceRecordObj|AssetBorrowingObj|AssetBorrowingDetailObj|AssetExtensionObj|AssetExtensionDetailObj|AssetReturnObj|AssetReturnDetailObj|AssetTransferObj|AssetTransferDetailObj|CustomerServiceSessionObj|DeviceMeterTypeObj|DeviceMeterObj|DeviceMeterReadingsObj|LogisticsOrderObj|ServiceCenterObj|LogisticsRecordObj|ServiceProgressObj|CustomerServiceStatusChangeRecordObj|WarehouseLocationObj|LocationStockObj|LocationBatchStockObj|DeviceLifeCycleObj|QuestionAnswerPairObj|EServiceGeneralRuleObj|KnowledgeDistributionRuleObj|KnowledgeDistributionRecordObj|KnowledgeArticleObj|KnowledgeSpaceObj|KnowledgeSpaceCategoryObj|KnowledgeIntegrationLogObj|CasesServicePersonnelObj

# crm apibus service路由配置
service_module_apibus_manufacturing=returned_goods_invoice,order_freeze_inventory_adjustment,returned_goods_invoice_ops,spare_parts_delivery_service,eservice_cases,eservice_device,eservice_biz,fee_detail,knowledge_category,cases_refobj_search,cases,batch,cost_adjustment,delivery_note,exchange_goods_note,exchange_return_note,fund_return_back ,outbound_delivery_note,purchase_order,requisition_note,serial_number,batch_sn,product,stock_bug_fix,stock_common_config,stock_config,stock_for_erp,stock_log,stock_module,stock_multi_unit,stock_report_form,stock_scan_code,stock,stock_special_operation,stock_statement,warehouse,stock_check_note,supplier,ReturnedGoodsInvoiceInterceptor,ReturnedGoodsInvoiceProductInterceptor,StockSalesOrderInterceptor,sales_forecast,accessory_exchange,service_project_category,StockSalesOrderProductInterceptor,inventory_details,logistics_query_front,accessory_path,erp_stock_biz,ErpStockObj,ErpWarehouseObj,interconnection_plugin,pop_request,call_center_advanced_setting,call_center,service_record_curl,service_record,telemarketing,call_center_tenant_bind,call_center_user_bind,sales_forecast_config,stock_ops,logistics_subscribe,engineer_info,spare_parts_application_handle_service,eservice_data_screen,employee_warehouse_detail,individual_stock_transactions,service_fault_tree,payment-record-plugin_add,eservice_service_request_assign,spare_parts,spare_parts_scan_code,stock_update,product_bar_code,inventory_freezing_ops,eservice_kpi_data_screen,salesOrderAddAfterStockHandler,salesOrderAddBeforeStockHandler,salesOrderEditAfterStockHandler,salesOrderEditBeforeStockHandler,salesOrderInvalidBeforeStockHandler,salesOrderInvalidAfterStockHandler,bom_core,eservice_index_statistic,salesOrderAddPostActStockHandler,salesOrderEditPostActStockHandler,salesOrderFlowCompletedPostActStockHandler,salesOrderInvalidPostActStockHandler,manufacturing_apl,stock_polling,asset_management,stock_order_close,StockOrderCloseBeforeHandler,StockOrderCloseActHandler,logistics_order,logistics_ops,cases_checkins,salesOrderFreezeAdjustmentHandler,salesOrderFreezeBatchAdjustmentHandler,bomCoreConfigOcrHandler,knowledgeSource,knowledge_service,eservice_datafresh_rest,mfgMetaDataRestService,device_meter_graph

# crm cep service路由配置
service_module_cep_manufacturing=returned_goods_invoice|order_freeze_inventory_adjustment|returned_goods_invoice_ops|eservice_cases|eservice_device|eservice_biz|fee_detail|knowledge_category|cases_refobj_search|cases|cost_adjustment|delivery_note|exchange_goods_note|exchange_return_note|fund_return_back |outbound_delivery_note|purchase_order|requisition_note|serial_number|batch_sn|product|stock_common_config|stock_config|stock_for_erp|stock_module|stock_multi_unit|stock_report_form|stock_scan_code|stock|stock_special_operation|stock_statement|warehouse|stock_check_note|supplier|accessory_exchange|service_project_category|StockSalesOrderProductInterceptor|inventory_details|logistics_query_front|accessory_path|erp_stock_biz|interconnection_plugin|pop_request|call_center_advanced_setting|call_center|service_record_curl|service_record|telemarketing|call_center_tenant_bind|call_center_user_bind|spare_parts_maintenance_task|eservice_data_screen|spare_parts_maintenance_task|eservice_cases_record_type|service_fault_tree|payment-record-plugin_add|eservice_service_request_assign|spare_parts|spare_parts_scan_code|stock_update|product_bar_code|inventory_freezing_ops|eservice_kpi_data_screen|device_life_circle|bom_core|eservice_index_statistic|manufacturing_apl|asset_borrowing|asset_borrowing_detail|asset_extending|asset_extending_detail|asset_return|asset_return_detail|asset_transfer|asset_transfer_detail|stock_polling|asset_management|logistics_order|logistics_ops|cases_checkins|salesOrderFreezeAdjustmentHandler|salesOrderFreezeBatchAdjustmentHandler|bomCoreConfigOcrHandler|knowledgeSource|knowledge_service|mfgMetaDataRestService|device_meter_graph

# 旧退货单企业
 

# 需要升级到新退货单的企业
rgi_upgrade_ei_list=

# 是否停止恢复个人库出入库记录 0是 1-否
is_stop_recover_in_out_record=1