#  ========================= 前端灰度控制 ============================
#  前端主站灰度， 注意跟eservice_h5_gray_ea保持一致！！！  已无用！！！
eservice_cases_gray_ea_list=["oseasy123", "fwt6006"]
# 前端H5灰度，跟eservice_cases_gray_ea_list保持一致！！！！  逗号隔开！！！
eservice_h5_gray_ea=
#  ========================= end ============================

#================================================= jacoco============================================================================

#================================================= provider ================================================
# 服务通后台 & 前端主站灰度， 注意跟eservice_h5_gray_ea保持一致！！！
eservice_cases_jacoco_ea_list=["fktest1947","796490_sandbox","fktest6663","fktest6948","fktest7557","fktest7556"]
#================================================== web ===================================================
eservice_web_jacoco_ei=720634,796490,806316,808782,816156,816155

#jacoco企业
jacoco_ei=720634,796490,806316,808782,816156,816155

#  ========================= 客服系统灰度控制 ============================
# 客服系统灰度配置
online_consult_gray_ea_list=[]
#  ========================= end ============================


web_gray_ei_vertical=${variables_gray_conf_NewUI.ei_list_format_vertical}|${variables_gray_conf_965.ei_list_format_vertical}
knowledge_ai_gray_ea_comma=${variables_gray_conf_NewUI.ea_list_format_comma},${variables_gray_conf_965.ea_list_format_comma}
knowledge_ai_gray_ea_semic=${variables_gray_conf_NewUI.ea_list_format_semicolon};${variables_gray_conf_965.ea_list_format_semicolon}
knowledge_ai_gray_ei_semic=${variables_gray_conf_NewUI.ei_list_format_semicolon};${variables_gray_conf_965.ei_list_format_semicolon}
knowledge_ai_gray_ei_comma=${variables_gray_conf_NewUI.ei_list_format_comma},${variables_gray_conf_965.ei_list_format_comma}

# ==========================  foneshare-gray 环境  ===============================
#  eservice-web项目 灰度变量  
web_gray_ei=715112,735849


#  eservice-cases\callcenter项目 灰度变量
common_gray_ea_list=["fktest1824","fwt6006"]

# fs-eservice-rest 接口 灰度变量
eservice_rest_gray_ei=715112,735849
# ==========================  end ===============================


# ==========================  foneshare-urgent 环境灰度变量 ===============================
#  eservice-web项目 灰度变量 
web_urgent_ei=

#  eservice-cases\callcenter项目 灰度变量 
eservice_urgent_ea_list=[]

# fs-eservice-rest 接口 灰度变量 
eservice_rest_urgent_ei=

# ========================== end ===============================

 
# ==========================  foneshare-stage 环境灰度变量 ===============================
#  eservice-web项目 灰度变量
web_stage_ei=806940

#  eservice-cases\callcenter项目 灰度变量
stage_gray_ea_list=["806940_sandbox"]

# fs-eservice-rest 接口 灰度变量
eservice_rest_stage_ei=806940
# ========================== end ===============================

# ==========================  foneshare-ruijie 环境灰度变量 ===============================
#  eservice-web项目 灰度变量 
web_ruijie_ei=730173


#  eservice-cases\callcenter项目 灰度变量 ,"ruijie2021"
ruijie_gray_ea_list=["ruijie2021"]

# fs-eservice-rest 接口 灰度变量 ,730173
eservice_rest_ruijie_ei=730173

# ========================== end ===============================




home_domain=www.fxiaoke.com

# 内部域名
eservice_internal_name_address=http://************:10875
#eservice_internal_name_address=************:45098 

# cases项目rest接口url, apibus 支持灰度路由
eservice_cases_rest_base_url=http://************:39414/fs-eservice-rest
# callcenter项目rest接口url
callcenter_rest_base_url=************:39414/fs-callcenter-rest
# 跨云调用
eservice_cases_global_base_url=http://api.fxiaokeglobal.local/eservice/rest
#专属云到纷享云
eservice_cloud_2_foneshare_url=http://************:39414/fs-eservice-rest

# 服务通菜单配置
eservice_menu_system_list=[{"systemName":"服务通","systemId":"2","systemConfigUrl":"http://************:10875/eservice/version/getEserviceMenu","systemWeight":1},{"systemName":"微客服","systemId":"1","systemConfigUrl":"${variables_endpoint.online_customer_service_base_url}/online/consult/menu/actions/get","systemWeight":2}]
# eservice_menu_system_list=[{"systemName":"服务通","systemId":"2","systemConfigUrl":"http://************:10875/eservice/version/getEserviceMenu","systemWeight":1},{"systemName":"微客服","systemId":"1","systemConfigUrl":"${variables_endpoint.online_customer_service_base_url}/online/consult/menu/actions/getOnlineMenu","systemWeight":2}]


# 服务通新版菜单配置（云上环境，不需要配置微客服菜单url）
eservice_new_menu_system_list=[{"systemName":"服务通新版菜单","systemId":"4","systemConfigUrl":"http://************:10875/eservice/version/getEserviceNewMenu","systemWeight":1}]

## 自定义对象相关 fs-paas-app-udobj-rest 
appframework_base_url=http://ncrmrest.nsvc.foneshare.cn

## fs-crm-fwt
object_ncrm_url=http://ncrm.nsvc.foneshare.cn

## 菜单 fs-webpage-customer-provider
crm_menu_base_url=http://************:49712

## 角色相关-新地址 fs-apibus-paas
role_base_new_url=http://udobj.nsvc.foneshare.cn

## 流程   fs-bpm
bpm_base_url=http://************:39414/fs-bpm-biz/

##  i18n-setting
i18n_setting_base_url=************:60419

## pass fs-paas-workflow
pass_workflow_base_url=http://paas.nsvc.foneshare.cn/

##报表  fs-bi-udf-report
reportview_base_url=http://${variables_endpoint.svc_apibus_global}/fs-bi-udf-report/

## 报表bi  fs-bi-crm-report-web
reportbi_base_url=http://${variables_endpoint.svc_apibus_global}/fs-bi-crm-report-web/

## 智能表单
smartform_base_url=http://crmsmartform.nsvc.foneshare.cn/smartform/

# 数据权限  data-auth-service
data_auth_base_url=http://************:63353/data-auth-service

# 按企业删除待办url   fs-todo
to_do_base_url=http://************:63054

## fs-apibus-global
api_bus_global_url=http://************:39414

## 互联平台  fs-enterprise-relation-biz
relation_base_url=${variables_endpoint.dns_er}

## 互联平台外网访问地址  fs-enterprise-relation-biz
relation_extranet_base_url=https://www.fxiaoke.com/fs-er-biz

# 获取 企业微信消息详情鉴权url
qywx_msg_auth_url=https://open.fxiaoke.com/qyweixin/doGetFunctionUrl

# 服务通常见问题跳转地址
service_management_faq_more_target_url=http://www.fxiaoke.com/mob/guide/fweixin/AS/3-0%E6%9C%8D%E5%8A%A1%E9%80%9A%E7%AE%80%E4%BB%8B.html

# 沃得农机gps url
wode_gps_url=http://36.155.113.70:1234

# 邮箱socks代理相关配置
use_socks_proxy=false


# mq消费配置 fs-eservice-gray
mq_consumer_process_name=
license_consumer_process_name=

#  eservice-support跳转
object_create_button_url=http://************:11353/API/v1/inner/object/button/service/create

# 线上客服服务地址，他们服务没有上云
online_customer_service_base_url=${variables_endpoint.online_customer_service_base_url}

# 内外部域名映射
inner_to_outer_mapping=

# 服务计划隐藏对象灰度EI列表
service_plan_ui_event_hidden_obj_ei_list=["ALL"]

# apibus
eservice_apibus_ncrm=http://${variables_endpoint.svc_apibus_ncrm}

# mq拉取间隔
pull_interval=0

# 全字段搜索
object_data_search_url=http://************:54527



## 备件维修计划灰度企业
eservice_spare_part_maintenance_plan_gray_ea=["fktest3551","fktest3453","fktest3544","fktest3546","fktest3547","754869_sandbox", "583193","fktest1781","fktest3476","fktest1783","fktest2351","fktest1812","genecast","nbrdzk","chuanri","xxwjhyp888","fjqbsp88","751132","szshengwei","fktest3476","fktest1783"]

# 文件预览请求地址
fs_fsc_provider_url=172.17.25.249:34820

# 域名
open_fxiaoke_domain=open.fxiaoke.com
eservice_web_url=http://************:39414/fs-eservice-web/eservice

# 服务通AI故障agent灰度企业列表
eservice_ai_fault_agent_gray_ei_list=795110,735849,806940