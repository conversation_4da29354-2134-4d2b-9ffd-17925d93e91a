{"tenants": {"fs-fmcg-ai-server": {"GRAY": "${variables_fmcg_gray.ei_list_fmcg_ai_gray}"}, "fs-crm-marketing": {"GRAY": "${variables_tenants.gray_crm_marketing_ei}"}, "erp-sync-data-web-gray": {"GRAY": ""}, "fs-bi-agent": {"GRAY": ""}, "fs-notice": {"GRAY": ""}, "fs-open-app-manage": {"GRAY": ""}, "fs-paas-recording-provider": {"GRAY": "${variables_qixin_tenants.record_gray_ei}"}, "fs-job-schedule": {"GRAY": "${variables_webpage_tenants.fs_paas_job_schedule_gray_ei}"}, "fs-paas-wishful-ocr-web": {"GRAY": "${variables_qixin_tenants.grayOcr}"}, "fs-crm-manufacturing": {"JACOCO": "${variables_manufacturing.jacoco_crm_ei}", "URGENT": "${variables_manufacturing.urgent_crm_ei}", "GRAY": "${variables_manufacturing.gray_crm_ei}", "STAGE": "${variables_manufacturing.stage_crm_ei}", "VIP": "${variables_manufacturing.vip_crm_ei}", "HAOLIYOU": "${variables_manufacturing.haoliyou_crm_ei}", "YQSL": "${variables_manufacturing.yqsl_crm_ei}", "RUIJIE": "${variables_manufacturing.ruijie_crm_ei}", "YINLU": "${variables_manufacturing.yinlu_crm_ei}"}, "fs-wechat-proxy-callback": {"GRAY": ""}, "fs-enterprise-relation-rest-web": {"GRAY": "${variables_enterpriserelation.gray_ei_crm_apibus}", "STAGE": "${variables_enterpriserelation.stage_ei_crm_apibus}", "URGENT": "${variables_enterpriserelation.urgent_ei_crm_apibus}"}, "fs-experience-account-provider": {"GRAY": "${variables_sandbox.change_set_gray_enterprises}"}, "fs-crm-notify": {"JACOCO": "${variables_qixin_tenants.jacoco}", "GRAY": "${variables_qixin_tenants.graycrmNotify}"}, "open-api-admin-web": {"GRAY": ""}, "paas-ai": {"JACOCO": "${variables_func.jacoco_paas_ai_tenantId}", "URGENT": "${variables_func.urgent_paas_ai_tenantId}", "STAGE": "${variables_func.stage_paas_ai_tenantId}", "GRAY": "${variables_func.gray_paas_ai_tenantId}"}, "fs-todo": {"JACOCO": "${variables_qixin_tenants.jacoco}", "GRAY": "${variables_qixin_tenants.grayTodo}"}, "fs-paas-auth": {"JACOCO": "${variables_paas_auth.jacocoTenantIds}", "URGENT": "${variables_paas_auth.urgentTenantIds}", "GRAY": "${variables_paas_auth.grayTenantIds}", "STAGE": "${variables_paas_auth.stageTenantIds}", "HAOLIYOU": "${variables_plat_global.plat_haoliyou_ei}", "YQSL": "${variables_plat_global.plat_yqsl_ei}", "VIP": "${variables_paas_auth.vipTenantIds}", "MEIFU": "${variables_gray_conf_meifu.ei_list_format_comma}", "YINLU": "${variables_plat_global.plat_yinlu_ei}"}, "fs-plat-auth-task": {"GRAY": ""}, "fs-crm-fmcg-service": {"URGENT": "${variables_fmcg_gray.ei_list_fmcg_tpm_urgent}", "GRAY": "${variables_fmcg_gray.ei_list_fmcg_tpm_gray}", "JACOCO": "${variables_fmcg_gray.ei_list_fmcg_crm_jacoco}", "HAOLIYOU": "${variables_fmcg_gray.ei_list_fmcg_tpm_haoliyou}", "YQSL": "${variables_fmcg_gray.ei_list_fmcg_tpm_yqsl}", "VIP": "${variables_fmcg_gray.ei_list_fmcg_tpm_vip}", "YINLU": "${variables_fmcg_gray.ei_list_fmcg_tpm_yinlu}"}, "fs-fmcg-service": {"URGENT": "${variables_fmcg_gray.ei_list_fmcg_service_urgent}", "HAOLIYOU": "${variables_fmcg_gray.ei_list_fmcg_service_haoliyou}", "YQSL": "${variables_fmcg_gray.ei_list_fmcg_service_yqsl}", "STAGE": "${variables_fmcg_gray.ei_list_fmcg_service_stage}"}, "fs-crm-fmcg-reward-web": {"GRAY": "${variables_fmcg_gray.ei_list_fmcg_task_reward_gray}", "JACOCO": "${variables_fmcg_gray.ei_list_fmcg_task_reward_jacoco}"}, "fs-paas-questionnaire-web": {"GRAY": "${variables_tenants.fs_paas_questionnaire_gray_ei}"}, "fs-paas-sign-provider": {"GRAY": "${variables_webpage_tenants.signGray_ei}"}, "fs-message": {"GRAY": "${variables_qixin_tenants.message_gray_ei}"}, "checkins-v2-task": {"GRAY": "${variables_fmcg_gray.ei_list_waiqinv2_gray}"}, "checkins-biz": {"ORION": "${variables_fmcg_gray.fmcg_alone_orion_ei}", "GRAY": "${variables_fmcg_gray.ei_list_waiqin_gray}"}, "data-auth-service": {"GRAY": "${variables_tenants.gray_data_auth_service_ei}", "HAOLIYOU": "${variables_tenants.haoliyou_data_auth_service_ei}", "YQSL": "${variables_tenants.yqsl_data_auth_service_ei}", "YINLU": "${variables_tenants.yinlu_data_auth_service_ei}", "VIP": "${variables_tenants.vip_data_auth_service_ei}"}, "fs-bi-uitype": {"GRAY": "${variables_bi_plat_gray.gray_eilist}", "stage": "${variables_bi_plat_gray.stage_eilist}", "vip": "${variables_bi_plat_gray.vip_eilist}"}, "fs-flow": {"GRAY": "${variables_flow_gray.engine_fcp_gray_ei}", "haoliyou": "707988", "yqsl": "735454", "yinlu": "721787", "VIP": "${variables_flow_gray.vip_ei}", "SVIP": "${variables_flow_gray.svip_ei}", "jacoco": "${variables_flow_gray.jcoco_fcp_ei}"}, "fs-flow-orch": {"GRAY": "${variables_flow_gray.flow_fcp_gray_ei}"}, "fs-bi-stat": {"foneshare01": "${variables_bi_plat_gray.foneshare01_eilist}", "jacoco": "${variables_bi_plat_gray.jacoco_eilist}", "GRAY": "${variables_bi_plat_gray.gray_eilist}", "stage": "${variables_bi_plat_gray.stage_eilist}", "vip": "${variables_bi_plat_gray.vip_eilist}"}, "udobj-rest4realtime": {"GRAY": "${variables_tenants.gray_udobj_ei}", "HAOLIYOU": "${variables_tenants.haoliyou_udobj_ei}", "STAGE": "${variables_tenants.stage_udobj_rest_ei}", "YQSL": "${variables_tenants.yqsl_udobj_ei}", "VIP": "${variables_tenants.vip_udobj_ei}", "YINLU": "${variables_tenants.yinlu_udobj_ei}"}, "fs-plat-service-biz": {"JACOCO": "${variables_plat_global.service_jacoco_ei}", "URGENT": "${variables_plat_global.platServiceUrgentEnterpirseIds}", "GRAY": "${variables_plat_global.platServiceGrayEnterpirseIds}", "HAOLIYOU": "${variables_plat_global.platServiceHaoliyouEI}", "RUIJIE": "${variables_plat_global.platServiceRuijieEI}", "YQSL": "${variables_plat_global.platServiceYqslEI}", "YINLU": "${variables_plat_global.platServiceYinluEI}", "STAGE": "${variables_plat_global.platServiceStageEnterpirseIds}"}, "fs-plat-service-provider": {"JACOCO": "${variables_plat_global.service_jacoco_ei}", "URGENT": "${variables_plat_global.platServiceProviderUrgentEnterpirseIds}", "HAOLIYOU": "${variables_plat_global.platServiceProviderHAOLIYOUEnterpirseIds}", "YINLU": "${variables_plat_global.platServiceProviderYINLUEnterpirseIds}", "YQSL": "${variables_plat_global.platServiceProviderYQSLEnterpirseIds}", "RUIJIE": "${variables_plat_global.platServiceProviderRUIJIEEnterpirseIds}", "STAGE": "${variables_plat_global.platServiceProviderStageEnterpirseIds}", "GRAY": "${variables_plat_global.platServiceProviderGrayEnterpirseIds}"}, "fs-schedule-task": {"JACOCO": "${variables_webpage_tenants.fs_schedule_task_jacoco_ei}", "GRAY": "${variables_webpage_tenants.fs_schedule_task_gray_ei}", "STAGE": "${variables_webpage_tenants.fs_schedule_task_stage_ei}", "VIP": "${variables_webpage_tenants.fs_schedule_task_vip_ei}", "SVIP": "${variables_webpage_tenants.fs_schedule_task_svip_ei}"}, "fs-paas-metadata-dataloader": {"STAGE": "${variables_tenants.stage_dataloader_ei}"}, "udobj-rest4flow": {"GRAY": "${variables_tenants.gray_udobj_ei}", "HAOLIYOU": "${variables_tenants.haoliyou_udobj_ei}", "STAGE": "${variables_tenants.stage_udobj_rest_ei}", "YQSL": "${variables_tenants.yqsl_udobj_ei}", "VIP": "${variables_tenants.vip_udobj_ei}", "YINLU": "${variables_tenants.yinlu_udobj_ei}"}, "fs-qixin-extension-provider": {"GRAY": ""}, "fs-bi-devops": {"GRAY": "${variables_bi_plat_gray.gray_eilist}"}, "fs-bi-permission": {"GRAY": "${variables_bi_plat_gray.gray_eilist}"}, "fs-bi-metadata": {"GRAY": "${variables_bi_plat_gray.gray_eilist}", "stage": "${variables_bi_plat_gray.stage_eilist}"}, "fs-crm-template": {"URGENT": "${variables_tenants.template_urgent_ei}", "STAGE": "${variables_tenants.stage_template_ei}", "GRAY": "${variables_tenants.gray_template_ei}", "VIP": "${variables_tenants.vip_udobj_ei}", "HAOLIYOU": "${variables_tenants.haoliyou_udobj_ei}", "JACOCO": "${variables_tenants.jacoco_udobj_ei}"}, "fs-bi-org": {"GRAY": "${variables_bi_plat_gray.gray_eilist}"}, "fs-bi-industry-interface": {"GRAY": "${variables_bi_plat.biIndustrySupply}", "PINGAN": "${variables_bi_plat.pinganIndustry}"}, "fs-bi-sqlengine": {"foneshare01": "${variables_bi_plat_gray.foneshare01_eilist}", "GRAY": "${variables_bi_plat_gray.gray_eilist}", "vip": "${variables_bi_plat_gray.vip_eilist}"}, "fs-metadata-rest": {"HAOLIYOU": "${variables_tenants.haoliyou_fs_metadata_rest_ei}", "YQSL": "${variables_tenants.yqsl_fs_metadata_rest_ei}", "YINLU": "${variables_tenants.yinlu_fs_metadata_rest_ei}", "GRAY": "815408,815409,590172", "STAGE": ""}, "bizconf": {"VIP": "${variables_tenants.haoliyou_fs_metadata_rest_ei},${variables_tenants.yqsl_fs_metadata_rest_ei},${variables_tenants.yinlu_fs_metadata_rest_ei}"}, "metadata-option": {"VIP": "${variables_tenants.haoliyou_fs_metadata_rest_ei},${variables_tenants.yqsl_fs_metadata_rest_ei},${variables_tenants.yinlu_fs_metadata_rest_ei}"}, "fs-expression-provider": {"GRAY": "590056,590057,590058,590059"}, "CHECKINS": {"GRAY": "${variables_fmcg_gray.ei_list_crm_callback_gray}"}, "fs-paas-org": {"HAOLIYOU": "${variables_tenants.haoliyou_fs_paas_org_ei}", "YQSL": "${variables_tenants.yqsl_fs_paas_org_ei}", "YINLU": "${variables_tenants.yinlu_fs_paas_org_ei}", "MEIFU": "${variables_gray_conf_meifu.ea_list_format_comma}"}, "fs-bi-transfer": {"GRAY": "${variables_bi_plat_gray.gray_eilist}"}, "fs-sync-data-all": {"GRAY": "${variables_fs_sync_data_global.gray_tenantIds}", "YINLU": "${variables_fs_sync_data_global.yinlu_tenantIds}"}, "fs-feeds-biz": {"JACOCO": "${variables_feeds_tenants.feedJacoco}", "URGENT": "${variables_feeds_tenants.feedUrgent}", "GRAY": "${variables_feeds_tenants.feedGray}", "STAGE": "${variables_feeds_tenants.feedStage}", "VIP": "${variables_feeds_tenants.feedVip}", "HAOLIYOU": "${variables_feeds_tenants.feedHaoliyou}", "YQSL": "${variables_feeds_tenants.feedYqsl}", "YINLU": "${variables_feeds_tenants.feedYinlu}"}, "approve-provider": {"JACOCO": "${variables_feeds_tenants.feedJacoco}", "URGENT": "${variables_feeds_tenants.feedUrgent}", "GRAY": "${variables_feeds_tenants.feedGray}", "STAGE": "${variables_feeds_tenants.feedStage}", "VIP": "${variables_feeds_tenants.feedVip}", "HAOLIYOU": "${variables_feeds_tenants.feedHaoliyou}", "YQSL": "${variables_feeds_tenants.feedYqsl}", "YINLU": "${variables_feeds_tenants.feedYinlu}"}, "fs-wechat-union-all": {"GRAY": ""}, "fs-enterprise-linkapp-web": {"GRAY": ""}, "fs-er-global-rest": {"GRAY": ""}, "fs-er-channel": {"GRAY": ""}, "fs-bi-metadata-ant": {"GRAY": "${variables_bi_plat_gray.gray_eilist}"}, "fs-paas-function-engine": {"URGENT": "${variables_func.func_urgent_tenantId}", "JAVA": "${variables_func.func_java_tenantId}", "GRAY": "${variables_func.func_gray_tenantId}", "STAGE": "${variables_func.func_stage_tenantId}", "HAOLIYOU": "${variables_func.func_haoliyou_tenantId}", "YQSL": "${variables_func.func_yqsl_tenantId}", "VIP": "${variables_func.func_vip_tenantId}", "SVIP": "${variables_func.func_svip_tenantId}", "DIDI": "${variables_func.func_didi_tenantId}", "MEIFU": "${variables_func.func_meifu_tenantId}", "RUIJIE": "${variables_func.func_ruijie_tenantId}", "YINLU": "${variables_func.func_yinlu_tenantId}"}, "fs-bi-dev-platform": {"foneshare01": "${variables_bi_plat_gray.foneshare01_eilist}", "jacoco": "${variables_bi_plat_gray.jacoco_eilist}", "GRAY": "${variables_bi_plat_gray.gray_eilist}", "stage": "${variables_bi_plat_gray.stage_eilist}", "vip": "${variables_bi_plat_gray.vip_eilist}"}, "fs-support-help": {"GRAY": "${variables_webpage_tenants.fs_support_help_gray_ei}"}, "fs-eservice-rest": {"URGENT": "${variables_eservice.eservice_rest_urgent_ei}", "GRAY": "${variables_eservice.eservice_rest_gray_ei}", "STAGE": "${variables_eservice.eservice_rest_stage_ei}", "ruijie": "${variables_eservice.eservice_rest_ruijie_ei}"}, "fs-email-rest": {"GRAY": "${variables_email.email_rest_gray_ei}"}, "fs-online-consult-rest": {"URGENT": "${variables_eservice.eservice_rest_urgent_ei}", "GRAY": "${variables_eservice.eservice_rest_gray_ei}", "STAGE": "${variables_eservice.eservice_rest_stage_ei}"}, "fs-online-consult-web": {"URGENT": "${variables_eservice.eservice_rest_urgent_ei}", "GRAY": "${variables_eservice.eservice_rest_gray_ei}", "STAGE": "${variables_eservice.eservice_rest_stage_ei}"}, "fs-callcenter-rest": {"URGENT": "${variables_eservice.eservice_rest_urgent_ei}", "GRAY": "${variables_eservice.eservice_rest_gray_ei}", "STAGE": "${variables_eservice.eservice_rest_stage_ei}", "ruijie": "${variables_eservice.eservice_rest_ruijie_ei}"}, "fs-eservice-web": {"URGENT": "${variables_eservice.eservice_rest_urgent_ei}", "GRAY": "${variables_eservice.eservice_rest_gray_ei}", "STAGE": "${variables_eservice.eservice_rest_stage_ei}", "ruijie": "${variables_eservice.eservice_rest_ruijie_ei}"}, "fs-bi-goal-web": {"GRAY": "${variables_bi_plat_gray.goal_gray_eilist}", "foneshare01": "${variables_bi_plat_gray.goal_foneshare01_eilist}"}, "fs-bpm-after-action": {"GRAY": "${variables_flow_gray.engine_fcp_gray_ei}", "haoliyou": "707988", "yqsl": "735454", "ruijie": "730173", "yinlu": "721787", "VIP": "${variables_flow_gray.vip_ei}", "svip": "${variables_flow_gray.svip_ei}", "jacoco": "${variables_flow_gray.jcoco_fcp_ei}"}, "pod": {"GRAY": ""}, "hamster-server": {"GRAY": ""}, "fs-crm-workflow": {"GRAY": "${variables_flow_gray.engine_fcp_gray_ei},1", "STAGE": "", "haoliyou": "", "yqsl": "735454", "yinlu": "", "vip": "${variables_flow_gray.vip_ei},${variables_flow_gray.svip_ei},707988,721787", "svip": "", "jacoco": "${variables_flow_gray.jcoco_fcp_ei}"}, "fs-paas-workflow": {"GRAY": "${variables_flow_gray.engine_fcp_gray_ei}", "STAGE": "", "ruijie": "721787", "haoliyou": "707988", "yqsl": "735454", "yinlu": "721787", "vip": "${variables_flow_gray.vip_ei}", "svip": "${variables_flow_gray.svip_ei}", "jacoco": "${variables_flow_gray.jcoco_fcp_ei}"}, "fs-bi-crm-report-web": {"foneshare01": "${variables_bi_plat_gray.foneshare01_eilist}", "jacoco": "${variables_bi_plat_gray.jacoco_eilist}", "GRAY": "${variables_bi_plat_gray.gray_eilist}", "stage": "${variables_bi_plat_gray.stage_eilist}", "vip": "${variables_bi_plat_gray.vip_eilist}"}, "fs-bpm-biz": {"GRAY": "${variables_flow_gray.engine_fcp_gray_ei}", "ruijie": "730173,804854,805092", "vip": "${variables_flow_gray.vip_ei}", "svip": "${variables_flow_gray.svip_ei}", "jacoco": "${variables_flow_gray.jcoco_fcp_ei}"}, "qixin-provider": {"GRAY": ""}, "fs-stage-propeller": {"GRAY": "${variables_flow_gray.engine_fcp_gray_ei}", "vip": "${variables_flow_gray.vip_ei},${variables_flow_gray.svip_ei},721787,735454,707988", "jacoco": "${variables_flow_gray.jcoco_fcp_ei}"}, "fs-paas-license": {"GRAY": "", "haoliyou": "", "yqsl": "", "yinlu": ""}, "fs-webpage-customer": {"URGENT": "${variables_webpage_tenants.urgent}", "JACOCO": "${variables_webpage_tenants.jacoco}", "STAGE": "${variables_tenants.stage_webpage_ei}", "HAOLIYOU": "${variables_webpage_tenants.haoliyou}", "YQSL": "${variables_webpage_tenants.yqsl}", "VIP": "${variables_webpage_tenants.vip}", "YINLU": "${variables_webpage_tenants.yinlu}"}, "fs-open-app-center-provider": {"URGENT": "${variables_webpage_tenants.open_urgent}", "JACOCO": "${variables_webpage_tenants.open_jacoco}", "STAGE": "${variables_webpage_tenants.open_gray}", "VIP": "${variables_webpage_tenants.open_vip}"}, "fs-qixin-search": {"GRAY": ""}, "fs-qixin-search-message": {"GRAY": ""}, "fs-user-extension": {"URGENT": "${variables_webpage_tenants.app_urgent}", "JACOCO": "", "STAGE": "${variables_tenants.stage_userext_ei}", "HAOLIYOU": "${variables_webpage_tenants.haoliyou}", "YQSL": "${variables_webpage_tenants.yqsl}", "YINLU": "${variables_webpage_tenants.yinlu}"}, "fs-organization-provider": {"JACOCO": "${variables_plat_global.org_jacoco_ei}", "GRAY": "${variables_plat_global.plat_org_provider_gray_ei}", "STAGE": "${variables_plat_global.plat_org_provider_stage_ei}", "HAOLIYOU": "${variables_plat_global.plat_haoliyou_ei}", "YQSL": "${variables_plat_global.plat_yqsl_ei}", "MEIFU": "${variables_gray_conf_meifu.ei_list_format_comma}", "YINLU": "${variables_plat_global.plat_yinlu_ei}"}, "fs-organization-provider-4data-auth": {"HAOLIYOU": "${variables_plat_global.plat_haoliyou_ei}", "YQSL": "${variables_plat_global.plat_yqsl_ei}", "YINLU": "${variables_plat_global.plat_yinlu_ei}", "RUIJIE": "${variables_plat_global.plat_ruijie_ei}", "GRAY": "${variables_plat_global.plat_org_provider_gray_ei}"}, "fs-organization-adapter-build-expansion": {"GRAY": ""}, "fs-plat-org-adapter-provider": {"GRAY": "${variables_plat_global.plat_org_adapter_provider_gray_ei}"}, "fs-ai-detector-provider": {"GRAY": "${variables_fmcg_gray.ei_list_ai_provider_gray}", "YQSL": "${variables_fmcg_gray.ei_list_ai_provider_yqsl}"}, "fs-organization-adapter": {"JACOCO": "${variables_plat_global.org_jacoco_ei}", "GRAY": "${variables_plat_global.plat_org_adapter_gray_ei}", "STAGE": "${variables_plat_global.plat_org_adapter_stage_ei}", "HAOLIYOU": "${variables_plat_global.platServiceProviderHAOLIYOUEnterpirseIds}", "YQSL": "${variables_plat_global.platServiceProviderYQSLEnterpirseIds}", "YINLU": "${variables_plat_global.platServiceProviderYINLUEnterpirseIds}", "RUIJIE": "${variables_plat_global.platServiceProviderRUIJIEEnterpirseIds}"}, "fs-organization-sqlserver-proxy-provider": {"GRAY": ""}, "fs-bi-export": {"GRAY": "${variables_bi_plat_gray.gray_eilist}"}, "fs-bi-sqlgenerator": {"GRAY": "${variables_bi_plat_gray.sql_generator_eilist}"}, "fs-feeds-next-provider": {"JACOCO": "${variables_feeds_tenants.feedJacoco}", "URGENT": "${variables_feeds_tenants.feedUrgent}", "GRAY": "${variables_feeds_tenants.feedGray}", "STAGE": "${variables_feeds_tenants.feedStage}", "VIP": "${variables_feeds_tenants.feedVip}", "HAOLIYOU": "${variables_feeds_tenants.feedHaoliyou}", "YQSL": "${variables_feeds_tenants.feedYqsl}", "YINLU": "${variables_feeds_tenants.feedYinlu}"}, "fs-social-web": {"JACOCO": "${variables_feeds_tenants.feedJacoco}", "URGENT": "${variables_feeds_tenants.feedUrgent}", "GRAY": "${variables_feeds_tenants.feedGray}", "STAGE": "${variables_feeds_tenants.feedStage}", "VIP": "${variables_feeds_tenants.feedVip}", "HAOLIYOU": "${variables_feeds_tenants.feedHaoliyou}", "YQSL": "${variables_feeds_tenants.feedYqsl}", "YINLU": "${variables_feeds_tenants.feedYinlu}"}, "fs-paas-app-udobj-rest": {"URGENT": "${variables_tenants.urgent_udobj_ei}", "GRAY": "${variables_tenants.gray_udobj_ei}", "TEST": "999999", "RUIJIE": "730173", "STAGE": "${variables_tenants.stage_udobj_rest_ei}", "HAOLIYOU": "${variables_tenants.haoliyou_udobj_ei}", "YQSL": "${variables_tenants.yqsl_udobj_ei}", "VIP": "${variables_tenants.vip_only_udobj_ei}", "SVIP": "${variables_tenants.svip_only_udobj_ei}", "MEIFU": "${variables_tenants.meifu_udobj_ei}", "YINLU": "${variables_tenants.yinlu_udobj_ei}"}, "fs-paas-app-udobj": {"URGENT": "${variables_tenants.urgent_udobj_ei}", "GRAY": "${variables_tenants.gray_udobj_ei}", "JACOCO": "${variables_tenants.jacoco_udobj_ei}", "STAGE": "${variables_tenants.stage_udobj_ei}", "HAOLIYOU": "${variables_tenants.haoliyou_udobj_ei}", "YQSL": "${variables_tenants.yqsl_udobj_ei}", "VIP": "${variables_tenants.vip_only_udobj_ei}", "SVIP": "${variables_tenants.svip_only_udobj_ei}", "RUIJIE": "${variables_tenants.ruijie_udobj_ei}", "MEIFU": "${variables_tenants.meifu_udobj_ei}", "YINLU": "${variables_tenants.yinlu_udobj_ei}"}, "fs-global-transaction": {"GRAY": "", "STAGE": "${variables_tenants.stage_udobj_rest_ei}"}, "fs-bi-mq": {"GRAY": "${variables_bi_plat_gray.gray_eilist}"}, "fs-appserver-checkins-v2": {"URGENT": "${variables_fmcg_gray.ei_list_waiqinv2_urgent}", "GRAY": "${variables_fmcg_gray.ei_list_new_waiqinv2_gray}", "STAGE": "${variables_fmcg_gray.ei_list_new_waiqinv2_stage}", "HAOLIYOU": "${variables_fmcg_gray.fmcg_orion_ei}", "YQSL": "${variables_fmcg_gray.fmcg_yqsl_ei}", "VIP": "${variables_fmcg_gray.vip_fs_crm_ei}", "SVIP": "${variables_fmcg_gray.fmcg_svip_fs_crm_ei}", "YINLU": "${variables_fmcg_gray.fmcg_ylspjt_ei}"}, "checkins-office-v2-server": {"GRAY": "${variables_fmcg_gray.ei_list_crm_callback_gray}"}, "fs-crm-sfa": {"URGENT": "${variables_tenants.urgent_fs_crm_sfa_ei}", "GRAY": "${variables_tenants.gray_fs_crm_sfa_ei}", "JACOCO": "${variables_tenants.jacoco_fs_crm_sfa_ei}", "STAGE": "${variables_tenants.stage_fs_crm_sfa_ei}", "HAOLIYOU": "${variables_tenants.haoliyou_fs_crm_sfa_ei}", "YQSL": "${variables_tenants.yqsl_fs_crm_sfa_ei}", "YINLU": "${variables_tenants.yinlu_fs_crm_sfa_ei}", "DIDI": "${variables_tenants.didi_fs_crm_sfa_ei}", "MEIFU": "${variables_tenants.meifu_fs_crm_sfa_ei}", "SVIP": "${variables_tenants.svip_fs_crm_sfa_ei}", "VIP": "${variables_tenants.vip_fs_crm_sfa_ei}"}, "fs-fmcg-sales-cgi": {"URGENT": "${variables_fmcg_gray.ea_list_sales_urgent}", "STAGE": "${variables_fmcg_gray.ei_list_sales_stage}", "YUANQI": "735454", "YINLU": "721787", "HAOLIYOU": "707988"}, "fs-support-feedback": {"GRAY": "${variables_webpage_tenants.fs_support_feedback_gray_ei}"}, "schedule": {"GRAY": ""}, "component": {"GRAY": "${variables_custom_component_tenants.gray}"}, "custom_component": {"GRAY": "${variables_custom_component_tenants.gray}"}, "auditlog-query-service": {"GRAY": "", "STAGE": "${variables_tenants.stage_auditlog_ei}"}, "smartform": {}, "fs-paas-score": {"STAGE": "", "JACOCO": "${variables_tenants.jacoco_udobj_ei}"}, "fs-support-nps": {"GRAY": "${variables_webpage_tenants.fs_support_nps_gray_ei}"}, "fs-bi-udf-report": {"foneshare01": "${variables_bi_plat_gray.foneshare01_eilist}", "jacoco": "${variables_bi_plat_gray.jacoco_eilist}", "GRAY": "${variables_bi_plat_gray.gray_eilist}", "stage": "${variables_bi_plat_gray.stage_eilist}", "vip": "${variables_bi_plat_gray.vip_eilist}"}, "fs-paas-rule": {"HAOLIYOU": "${variables_tenants.haoliyou_fs_paas_rule_ei}", "YQSL": "${variables_tenants.yqsl_fs_paas_rule_ei}", "YINLU": "${variables_tenants.yinlu_fs_paas_rule_ei}"}, "fs-crm": {"SVIP": "${variables_tenants.svip_fs_crm_ei}", "VIP": "${variables_tenants.vip_fs_crm_ei}", "STAGE": "${variables_tenants.stage_fs_crm_ei}", "URGENT": "${variables_tenants.urgent_fs_crm_ei}", "haoliyou": "${variables_tenants.haoliyou_fs_crm_ei}", "yqsl": "${variables_tenants.yqsl_fs_crm_ei}", "yinlu": "${variables_tenants.yinlu_fs_crm_ei}"}, "fs-sail-order": {"URGENT": "", "STAGE": "", "YQSL": ""}, "i18n-setting": {"GRAY": ""}}, "common": {"fs-fmcg-ai-server": {"rootApplication": true}, "fs-crm-marketing": {"rootApplication": true}, "erp-sync-data-web-gray": {"rootApplication": true}, "fs-notice": {"rootApplication": true}, "fs-crm-manufacturing": {"rootApplication": true}, "fs-job-schedule": {"rootApplication": true}, "fs-crm-enterprisepay": {"rootApplication": true}, "fs-experience-account-provider": {"rootApplication": true}, "fs-wechat-proxy-callback": {"rootApplication": false}, "fs-enterprise-relation-rest-web": {"rootApplication": false}, "open-api-admin-web": {"rootApplication": true}, "paas-ai": {"rootApplication": true}, "custom_component": {"rootApplication": true}, "fs-crm-fmcg-service": {"rootApplication": true}, "fs-fmcg-service": {"rootApplication": true}, "fs-crm-fmcg-reward-web": {"rootApplication": true}, "fs-eservice-rest": {"rootApplication": true}, "fs-email-rest": {"rootApplication": true}, "fs-online-consult-rest": {"rootApplication": true}, "fs-online-consult-web": {"rootApplication": true}, "fs-callcenter-rest": {"rootApplication": true}, "fs-eservice-web": {"rootApplication": true}, "fs-paas-wishful-ocr-web": {"rootApplication": false}, "fs-plat-org-adapter-provider": {"rootApplication": true}, "fs-bi-goal-web": {"rootApplication": true}, "fs-plat-auth-task": {"rootApplication": true}, "fs-paas-sign-provider": {"rootApplication": true}, "checkins-v2-task": {"rootApplication": true}, "checkins-biz": {"rootApplication": true}, "fs-bi-agent": {"rootApplication": true}, "fs-bi-crm-report-web": {"rootApplication": true}, "fs-stage-propeller": {"rootApplication": true}, "fs-plat-service-biz": {"rootApplication": true}, "fs-plat-service-provider": {"rootApplication": true}, "fs-open-app-manage": {"rootApplication": true}, "udobj-rest4realtime": {"rootApplication": true}, "fs-schedule-task": {"rootApplication": true}, "fs-paas-metadata-dataloader": {"rootApplication": true}, "udobj-rest4flow": {"rootApplication": true}, "fs-webpage-customer": {"rootApplication": true}, "fs-open-app-center-provider": {"rootApplication": true}, "fs-qixin-search-message": {"rootApplication": true}, "fs-ai-detector-provider": {"rootApplication": true}, "fs-qixin-search": {"rootApplication": true}, "fs-user-extension": {"rootApplication": true}, "fs-crm-template": {"rootApplication": true}, "fs-bi-industry-interface": {"rootApplication": true}, "CHECKINS": {"rootApplication": true}, "fs-paas-app-udobj-rest": {"rootApplication": true}, "fs-paas-app-udobj": {"rootApplication": true}, "fs-global-transaction": {"rootApplication": true}, "fs-todo": {"rootApplication": true}, "fs-paas-recording-provider": {"rootApplication": true}, "fs-crm-notify": {"rootApplication": false}, "fs-appserver-checkins-v2": {"rootApplication": true}, "checkins-office-v2-server": {"rootApplication": true}, "fs-fmcg-sales-cgi": {"rootApplication": true}, "fs-support-feedback": {"rootApplication": true}, "fs-sync-data-all": {"rootApplication": true}, "auditlog-query-service": {"rootApplication": true}, "fs-feeds-biz": {"rootApplication": true}, "approve-provider": {"rootApplication": true}, "fs-wechat-union-all": {"rootApplication": false}, "fs-enterprise-linkapp-web": {"rootApplication": false}, "fs-er-global-rest": {"rootApplication": false}, "fs-er-channel": {"rootApplication": false}, "fs-paas-score": {"rootApplication": true}, "fs-support-nps": {"rootApplication": true}, "fs-paas-function-engine": {"rootApplication": true, "enableSandboxEnv": true, "tenantEnvOrder": ["URGENT", "JAVA", "GRAY"]}, "fs-bi-udf-report": {"rootApplication": true}, "fs-bi-dev-platform": {"rootApplication": true}, "fs-social-web": {"rootApplication": true}, "fs-support-help": {"rootApplication": true}, "fs-crm-sfa": {"rootApplication": true}, "fs-crm": {"rootApplication": true}, "fs-sail-order": {"rootApplication": true}, "i18n-setting": {"rootApplication": true}}, "upstreams": [{"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:15018"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_fs_fmcg_ai_server}"]}}, "readTimeout": 300000, "moduleName": "fs-fmcg-ai-server", "maxConnections": 50}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:16180"]}, "GRAY": {"serverList": ["************:16180"]}}, "readTimeout": 20000, "moduleName": "fs-crm-marketing", "maxConnections": 500}, {"environments": {"NORMAL": {"serverList": ["************:19252"]}}, "readTimeout": 300000, "moduleName": "erp-sync-data-web-gray", "maxConnections": 500}, {"environments": {"NORMAL": {"serverList": ["http://fs-notice.${variables_endpoint.namespace}"]}}, "readTimeout": 20000, "moduleName": "fs-notice", "maxConnections": 500}, {"environments": {"JACOCO": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_server_jacoco}"]}, "URGENT": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_server_urgent}"]}, "GRAY": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_server_gray}"]}, "STAGE": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_server_stage}"]}, "VIP": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_rest_server_vip}"]}, "HAOLIYOU": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_server_haoliyou}"]}, "YQSL": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_server_yqsl}"]}, "RUIJIE": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_rest_server_ruijie}"]}, "YINLU": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_server_yinlu}"]}, "NORMAL": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_rest_server}", "${variables_manufacturing.fs_crm_manufacturing_rest_server_01}"]}}, "readTimeout": 20000, "moduleName": "fs-crm-manufacturing", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:36854"], "maxConnections": 50}}, "readTimeout": 10000, "moduleName": "fs-open-app-manage", "maxConnections": 10}, {"environments": {"NORMAL": {"serverList": ["${variables_enterprisepay.fs_server_default}"]}}, "readTimeout": 20000, "moduleName": "fs-crm-enterprisepay", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["*************:15119"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:34567"], "maxConnections": 500}}, "readTimeout": 15000, "moduleName": "fs-paas-wishful-ocr-web", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["*************:19004"]}, "NORMAL": {"serverList": ["************:39879"]}}, "readTimeout": 20000, "moduleName": "fs-job-schedule", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["${variables_endpoint.svc_fs_wechat_union_all}/fs-wechat-proxy-callback"]}}, "readTimeout": 20000, "moduleName": "fs-wechat-proxy-callback", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"URGENT": {"serverList": ["${variables_enterpriserelation.fs_crm_er_apibus_server_urgent}/fs-enterprise-relation-rest-web"]}, "GRAY": {"serverList": ["${variables_enterpriserelation.fs_crm_er_apibus_server_gray}/fs-enterprise-relation-rest-web"]}, "STAGE": {"serverList": ["${variables_enterpriserelation.fs_crm_er_apibus_server_stage}/fs-enterprise-relation-rest-web"]}, "VIP": {"serverList": ["${variables_enterpriserelation.fs_crm_er_apibus_server_vip}/fs-enterprise-relation-rest-web"]}, "HAOLIYOU": {"serverList": ["${variables_enterpriserelation.fs_crm_er_apibus_server_haoliyou}/fs-enterprise-relation-rest-web"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_fs_enterprise_relation_biz}/fs-enterprise-relation-rest-web"]}}, "readTimeout": 20000, "moduleName": "fs-enterprise-relation-rest-web", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:37713"]}, "NORMAL": {"serverList": ["************:32622"]}}, "readTimeout": 20000, "moduleName": "fs-experience-account-provider", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:11529/openapiadmin"]}}, "readTimeout": 20000, "moduleName": "open-api-admin-web", "maxConnections": 500}, {"environments": {"JACOCO": {"serverList": ["*************:19253/fs-paas-ai"]}, "URGENT": {"serverList": ["************:11322/fs-paas-ai"]}, "STAGE": {"serverList": ["*************:19566/fs-paas-ai"]}, "GRAY": {"serverList": ["*************:59697/fs-paas-ai"]}, "NORMAL": {"serverList": ["************:63893/fs-paas-ai"]}}, "readTimeout": 360000, "moduleName": "paas-ai", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["************:12266"]}, "URGENT": {"serverList": ["************:12023"]}, "GRAY": {"serverList": ["************:10171"]}, "HAOLIYOU": {"serverList": ["************:65240"]}, "YQSL": {"serverList": ["************:41933"]}, "YINLU": {"serverList": ["************:34271"]}, "VIP": {"serverList": ["************:55960"], "maxConnections": 800}, "NORMAL": {"serverList": ["************:43259"], "maxConnections": 800}}, "readTimeout": 20000, "moduleName": "fs-crm-fmcg-service", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"URGENT": {"serverList": ["************:13248"]}, "HAOLIYOU": {"serverList": ["************:31336"]}, "YQSL": {"serverList": ["************:18645"]}, "STAGE": {"serverList": ["************:13685"], "maxConnections": 800}, "NORMAL": {"serverList": ["************:58733"], "maxConnections": 800}}, "readTimeout": 20000, "moduleName": "fs-fmcg-service", "maxConnections": 200}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["************:16153"], "maxConnections": 800}, "GRAY": {"serverList": ["************:10030"]}, "NORMAL": {"serverList": ["************:11127"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "fs-crm-fmcg-reward-web", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["${variables_endpoint.svc_fs_wechat_union_all}/fs-wechat-union-all"]}}, "readTimeout": 20000, "moduleName": "fs-wechat-union-all", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:41716/fs-er-global-rest"]}}, "readTimeout": 20000, "moduleName": "fs-er-global-rest", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"URGENT": {"serverList": ["${variables_enterpriserelation.fs_crm_er_apibus_server_urgent}/fs-er-channel"]}, "GRAY": {"serverList": ["${variables_enterpriserelation.fs_crm_er_apibus_server_gray}/fs-er-channel"]}, "STAGE": {"serverList": ["${variables_enterpriserelation.fs_crm_er_apibus_server_stage}/fs-er-channel"]}, "VIP": {"serverList": ["${variables_enterpriserelation.fs_crm_er_apibus_server_vip}/fs-er-channel"]}, "HAOLIYOU": {"serverList": ["${variables_manufacturing.fs_crm_manufacturing_server_haoliyou}/fs-er-channel"]}, "NORMAL": {"serverList": ["************:63814/fs-er-channel"]}}, "readTimeout": 20000, "moduleName": "fs-er-channel", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:41716/fs-enterprise-linkapp-web"]}}, "readTimeout": 20000, "moduleName": "fs-enterprise-linkapp-web", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:11931"], "maxConnections": 500}, "URGENT": {"serverList": ["************:12098"], "maxConnections": 500}, "STAGE": {"serverList": ["************:15029"], "maxConnections": 500}, "ruijie": {"serverList": ["************:43326"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:59539"], "maxConnections": 500}}, "readTimeout": 60000, "moduleName": "fs-eservice-rest", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:13384"], "maxConnections": 500}, "NORMAL": {"serverList": ["172.17.32.61:8199", "172.17.32.62:8199"], "maxConnections": 500}}, "readTimeout": 30000, "moduleName": "fs-email-rest", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:17451"], "maxConnections": 500}, "URGENT": {"serverList": ["************:13799"], "maxConnections": 500}, "STAGE": {"serverList": ["************:14688"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:46727"], "maxConnections": 500}}, "readTimeout": 30000, "moduleName": "fs-online-consult-rest", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:17505"], "maxConnections": 500}, "URGENT": {"serverList": ["************:10130"], "maxConnections": 500}, "STAGE": {"serverList": ["************:15897"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:31444"], "maxConnections": 500}}, "readTimeout": 60000, "moduleName": "fs-online-consult-web", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:11931"], "maxConnections": 500}, "URGENT": {"serverList": ["************:12098"], "maxConnections": 500}, "STAGE": {"serverList": ["************:15029"], "maxConnections": 500}, "ruijie": {"serverList": ["************:43326"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:59539"], "maxConnections": 500}}, "readTimeout": 30000, "moduleName": "fs-callcenter-rest", "maxConnections": 500}, {"poolKeepAliveTime": 10, "environments": {"JACOCO": {"serverList": ["************:12528"]}, "GRAY": {"serverList": ["************:14172"]}, "NORMAL": {"serverList": ["************:63054"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "fs-todo", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["*************:18810"]}, "NORMAL": {"serverList": ["************:62838"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "fs-paas-recording-provider", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"JACOCO": {"serverList": ["************:11551"]}, "GRAY": {"serverList": ["************:17351"]}, "NORMAL": {"serverList": ["************:45531"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "fs-crm-notify", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:16509"], "maxConnections": 500}, "YQSL": {"serverList": ["************:45441"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:37462"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "fs-ai-detector-provider", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:13996"], "maxConnections": 500}, "URGENT": {"serverList": ["************:12256"], "maxConnections": 500}, "STAGE": {"serverList": ["************:10875"], "maxConnections": 500}, "ruijie": {"serverList": ["************:48558"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:45098"], "maxConnections": 500}}, "readTimeout": 30000, "moduleName": "fs-eservice-web", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["************:15490"], "maxConcurrencyRequests": 400}, "URGENT": {"serverList": ["************:18099"], "maxConnections": 100}, "GRAY": {"serverList": ["************:12598"], "maxConnections": 100}, "HAOLIYOU": {"serverList": ["************:51289"], "maxConnections": 100}, "YINLU": {"serverList": ["************:45863"], "maxConnections": 100}, "YQSL": {"serverList": ["************:43692"], "maxConnections": 100}, "RUIJIE": {"serverList": ["************:39797"], "maxConnections": 100}, "STAGE": {"serverList": ["************:12916"], "maxConnections": 100}, "NORMAL": {"serverList": ["************:40847"], "maxConnections": 100}}, "readTimeout": 20000, "moduleName": "fs-plat-service-biz", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["************:19726"], "maxConcurrencyRequests": 400}, "URGENT": {"serverList": ["************:17565"], "maxConnections": 100}, "GRAY": {"serverList": ["************:18965"], "maxConnections": 100}, "HAOLIYOU": {"serverList": ["************:58585"], "maxConnections": 100}, "YINLU": {"serverList": ["************:45821"], "maxConnections": 100}, "YQSL": {"serverList": ["************:41120"], "maxConnections": 100}, "RUIJIE": {"serverList": ["************:37795"], "maxConnections": 100}, "STAGE": {"serverList": ["************:11666"], "maxConnections": 100}, "NORMAL": {"serverList": ["************:49725"], "maxConnections": 100}}, "readTimeout": 20000, "moduleName": "fs-plat-service-provider", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["************:41919"]}, "URGENT": {"serverList": ["************:12773"]}, "GRAY": {"serverList": ["************:11606"]}, "STAGE": {"serverList": ["************:13705"], "maxConnections": 500}, "VIP": {"serverList": ["************:17473"], "maxConnections": 500}, "HAOLIYOU": {"serverList": ["************:11567"], "maxConnections": 500}, "YQSL": {"serverList": ["************:42065"], "maxConnections": 500}, "YINLU": {"serverList": ["************:11541"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:41963"], "maxConnections": 500}}, "readTimeout": 20000, "moduleName": "fs-feeds-next-provider", "maxConnections": 5000}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["************:41919"]}, "URGENT": {"serverList": ["************:12773"]}, "GRAY": {"serverList": ["************:11606"]}, "STAGE": {"serverList": ["************:13705"], "maxConnections": 500}, "VIP": {"serverList": ["************:17473"], "maxConnections": 500}, "HAOLIYOU": {"serverList": ["************:11567"], "maxConnections": 500}, "YQSL": {"serverList": ["************:42065"], "maxConnections": 500}, "YINLU": {"serverList": ["************:11541"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:41963"], "maxConnections": 500}}, "readTimeout": 20000, "moduleName": "approve-provider", "maxConnections": 5000}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["************:17949"]}, "URGENT": {"serverList": ["************:17945"]}, "GRAY": {"serverList": ["************:15433"]}, "STAGE": {"serverList": ["************:18952"], "maxConnections": 500}, "VIP": {"serverList": ["************:11545"], "maxConnections": 500}, "HAOLIYOU": {"serverList": ["************:38623"], "maxConnections": 500}, "YQSL": {"serverList": ["************:34984"], "maxConnections": 500}, "YINLU": {"serverList": ["************:33583"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:46478"], "maxConnections": 500}}, "readTimeout": 20000, "moduleName": "fs-social-web", "maxConnections": 5000}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:12875"]}, "YINLU": {"serverList": ["************:55586"]}, "NORMAL": {"serverList": ["${variables_fs_sync_data.normal_addr}"]}}, "readTimeout": 20000, "moduleName": "fs-sync-data-all", "maxConnections": 500}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["*************:19269"], "maxConnections": 500}, "STAGE": {"serverList": ["*************:16520"], "maxConnections": 500}, "JACOCO": {"serverList": ["*************:10072"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:58030"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "fs-paas-score", "maxConnections": 100}, {"environments": {"foneshare01": {"serverList": ["${variables_endpoint.svc_bi_stat_foneshare01}"]}, "jacoco": {"serverList": ["${variables_endpoint.svc_bi_stat_jacoco}"]}, "GRAY": {"serverList": ["${variables_endpoint.svc_bi_stat_gray}"]}, "stage": {"serverList": ["*************:19575"]}, "vip": {"serverList": ["*************:64944"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_stat}"]}}, "readTimeout": 300000, "moduleName": "fs-bi-stat", "maxConnections": 100}, {"environments": {"foneshare01": {"serverList": ["${variables_endpoint.svc_bi_dev_platform_foneshare01}"]}, "jacoco": {"serverList": ["${variables_endpoint.svc_bi_dev_platform_jacoco}"]}, "GRAY": {"serverList": ["${variables_endpoint.svc_bi_dev_platform_gray}"]}, "stage": {"serverList": ["${variables_endpoint.svc_bi_dev_platform}"]}, "vip": {"serverList": ["${variables_endpoint.svc_bi_dev_platform}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_dev_platform}"]}}, "readTimeout": 300000, "moduleName": "fs-bi-dev-platform", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["*************:12240"]}, "GRAY": {"serverList": ["*************:15119"]}, "NORMAL": {"serverList": ["************:34567"], "maxConnections": 50}}, "readTimeout": 20000, "moduleName": "fs-paas-sign-provider", "maxConnections": 10}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_mq_gray}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_mq}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-mq", "maxConnections": 100}, {"environments": {"foneshare01": {"serverList": ["${variables_endpoint.svc_bi_report_foneshare01}"]}, "jacoco": {"serverList": ["${variables_endpoint.svc_bi_report_jacoco}"]}, "GRAY": {"serverList": ["${variables_endpoint.svc_bi_report_gray}"]}, "stage": {"serverList": ["${variables_endpoint.svc_bi_report_stage}"]}, "vip": {"serverList": ["${variables_endpoint.svc_bi_report_vip}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_report}"]}}, "readTimeout": 600000, "moduleName": "fs-bi-crm-report-web", "maxConnections": 100}, {"environments": {"NORMAL": {"serverList": ["${variables_endpoint.svc_bi_agent}"]}}, "readTimeout": 600000, "moduleName": "fs-bi-agent", "maxConnections": 100}, {"environments": {"foneshare01": {"serverList": ["${variables_endpoint.svc_bi_udf_report_foneshare01}"]}, "jacoco": {"serverList": ["${variables_endpoint.svc_bi_udf_report_jacoco}"]}, "GRAY": {"serverList": ["${variables_endpoint.svc_bi_udf_report_gray}"]}, "stage": {"serverList": ["${variables_endpoint.svc_bi_udf_report_stage}"]}, "vip": {"serverList": ["${variables_endpoint.svc_bi_udf_report_vip}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_udf_report}"]}}, "readTimeout": 1800000, "moduleName": "fs-bi-udf-report", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"HAOLIYOU": {"serverList": ["************:30221"]}, "STAGE": {"serverList": ["*************:18393", "*************:15376"]}, "GRAY": {"serverList": ["*************:15031"]}, "YQSL": {"serverList": ["************:34705"]}, "VIP": {"serverList": ["************:48071"]}, "NORMAL": {"serverList": ["************:11539", "************:51091"]}, "YINLU": {"serverList": ["************:54629"]}}, "readTimeout": 10000, "moduleName": "udobj-rest4flow", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"HAOLIYOU": {"serverList": ["************:41585"]}, "STAGE": {"serverList": ["*************:15854", "*************:11665", "*************:14738"]}, "GRAY": {"serverList": ["*************:15031"]}, "YQSL": {"serverList": ["************:54161"]}, "VIP": {"serverList": ["************:38801"]}, "NORMAL": {"serverList": ["************:14596", "************:17424"]}, "YINLU": {"serverList": ["************:53061"]}}, "readTimeout": 10000, "moduleName": "udobj-rest4realtime", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_uitype_gray}"]}, "stage": {"serverList": ["${variables_endpoint.svc_bi_uitype_stage}"]}, "vip": {"serverList": ["${variables_endpoint.svc_bi_uitype_vip}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_uitype}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-uitype", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"NORMAL": {"serverList": ["************:59053"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "fs-expression-provider", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_export_gray}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_export}"]}}, "readTimeout": 1800000, "moduleName": "fs-bi-export", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_goal_gray}"]}, "foneshare01": {"serverList": ["${variables_endpoint.svc_bi_goal_foneshare01}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_goal}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-goal-web", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_permission_gray}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_permission}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-permission", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_metadata_gray}"]}, "stage": {"serverList": ["${variables_endpoint.svc_bi_metadata_stage}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_metadata}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-metadata", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_metadata_ant_gray}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_metadata_ant}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-metadata-ant", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_transfer_gray}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_transfer}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-transfer", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_devops_gray}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_devops}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-devops", "maxConnections": 100}, {"environments": {"foneshare01": {"serverList": ["${variables_endpoint.svc_bi_sqlengine_foneshare01}"]}, "GRAY": {"serverList": ["${variables_endpoint.svc_bi_sqlengine_gray}"]}, "vip": {"serverList": ["${variables_endpoint.svc_bi_sqlengine_vip}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_sqlengine}"]}}, "readTimeout": 240000, "moduleName": "fs-bi-sqlengine", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_org_gray}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_org}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-org", "maxConnections": 100}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_bi_sqlgenerator_gray}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_bi_sqlgenerator}"]}}, "readTimeout": 20000, "moduleName": "fs-bi-sqlgenerator", "maxConnections": 100}, {"environments": {"JAVA": {"serverList": ["************:32243"]}, "URGENT": {"serverList": ["************:47932"]}, "GRAY": {"serverList": ["*************:30818"]}, "STAGE": {"serverList": ["*************:48465"]}, "HAOLIYOU": {"serverList": ["************:57201"]}, "YQSL": {"serverList": ["************:59922"]}, "VIP": {"serverList": ["************:60924"]}, "SVIP": {"serverList": ["************:18617"]}, "DIDI": {"serverList": ["*************:36322"], "maxConnections": 1000}, "MEIFU": {"serverList": ["*************:13691"], "maxConnections": 1000}, "RUIJIE": {"serverList": ["************:44916"]}, "SANDBOX": {"serverList": ["************:39385"]}, "NORMAL02": {"serverList": ["************:50742"]}, "NORMAL": {"serverList": ["************:36194", "************:50742", "************:32243"]}, "YINLU": {"serverList": ["************:35228"]}}, "readTimeout": 60000, "moduleName": "fs-paas-function-engine", "maxConnections": 500}, {"poolKeepAliveTime": 10, "environments": {"JACOCO": {"serverList": ["************:18358"]}, "URGENT": {"serverList": ["************:15229"]}, "GRAY": {"serverList": ["************:17013"]}, "STAGE": {"serverList": ["************:16518"], "maxConnections": 500}, "VIP": {"serverList": ["************:12512"], "maxConnections": 500}, "HAOLIYOU": {"serverList": ["************:30452"], "maxConnections": 500}, "YQSL": {"serverList": ["************:44864"], "maxConnections": 500}, "YINLU": {"serverList": ["************:57876"], "maxConnections": 500}, "NORMAL": {"serverList": ["************:47360"], "maxConnections": 500}}, "readTimeout": 30000, "moduleName": "fs-feeds-biz", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["*************:50872"]}, "NORMAL": {"serverList": ["*************:19244"], "maxConnections": 500}, "PINGAN": {"serverList": ["*************:19244"], "maxConnections": 500}}, "readTimeout": 20000, "moduleName": "fs-bi-industry-interface", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["************:14222"]}, "STAGE": {"serverList": ["************:11544"]}, "haoliyou": {"serverList": ["************:34937"]}, "yqsl": {"serverList": ["************:37757"]}, "vip": {"serverList": ["************:46158"]}, "svip": {"serverList": ["************:43944"]}, "yinlu": {"serverList": ["************:31249"]}, "NORMAL": {"serverList": ["************:48036"], "maxConnections": 500}, "jacoco": {"serverList": ["************:10607"], "maxConnections": 500}}, "readTimeout": 32000, "moduleName": "fs-crm-workflow", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["*************:12949"]}, "STAGE": {"serverList": ["*************:10715"]}, "haoliyou": {"serverList": ["************:63630"]}, "yqsl": {"serverList": ["************:41324"]}, "vip": {"serverList": ["************:51207"]}, "svip": {"serverList": ["************:52278"]}, "yinlu": {"serverList": ["************:62951"]}, "ruijie": {"serverList": ["************:14469"]}, "NORMAL": {"serverList": ["************:59920"], "maxConnections": 500}, "jacoco": {"serverList": ["*************:19637"], "maxConnections": 500}}, "readTimeout": 32000, "moduleName": "fs-paas-workflow", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["************:17340"]}, "haoliyou": {"serverList": ["************:32941"]}, "yqsl": {"serverList": ["************:44049"]}, "vip": {"serverList": ["************:54274"]}, "svip": {"serverList": ["************:50524"]}, "yinlu": {"serverList": ["************:52459"]}, "NORMAL": {"serverList": ["************:58980"], "maxConnections": 500}, "jacoco": {"serverList": ["************:18381"], "maxConnections": 500}}, "readTimeout": 32000, "moduleName": "fs-stage-propeller", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:19569"]}, "HAOLIYOU": {"serverList": ["************:61888"]}, "YQSL": {"serverList": ["************:53696"]}, "NORMAL": {"serverList": ["************:63353", "************:15626"], "maxConnections": 800}, "YINLU": {"serverList": ["************:54411"]}, "VIP": {"serverList": ["************:10579"]}}, "readTimeout": 130000, "moduleName": "data-auth-service", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"HAOLIYOU": {"serverList": ["************:63260"]}, "YQSL": {"serverList": ["************:52630"]}, "NORMAL": {"serverList": ["************:54833"], "maxConnections": 800}, "YINLU": {"serverList": ["************:48875"]}, "MEIFU": {"serverList": ["************:14135"]}}, "readTimeout": 30000, "moduleName": "fs-paas-org", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"HAOLIYOU": {"serverList": ["************:62994"]}, "YQSL": {"serverList": ["************:45043"]}, "NORMAL": {"serverList": ["************:47855"], "maxConnections": 800}, "YINLU": {"serverList": ["************:59860"]}}, "readTimeout": 30000, "moduleName": "fs-paas-rule", "maxConnections": 500}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["************:10798"]}, "STAGE": {"serverList": ["************:13449"]}, "NORMAL": {"serverList": ["************:34674"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "auditlog-query-service", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["${variables_paas_auth.jacocoServer}"]}, "URGENT": {"serverList": ["${variables_paas_auth.urgentServer}"]}, "GRAY": {"serverList": ["${variables_paas_auth.grayServer}"]}, "HAOLIYOU": {"serverList": ["${variables_paas_auth.haoliyouServer}"]}, "YQSL": {"serverList": ["${variables_paas_auth.yqslServer}"]}, "YINLU": {"serverList": ["${variables_paas_auth.yinluServer}"]}, "STAGE": {"serverList": ["${variables_paas_auth.stageServer}"]}, "VIP": {"serverList": ["${variables_paas_auth.vipServer}"], "maxConnections": 800}, "MEIFU": {"serverList": ["${variables_paas_auth.meifuServer}"]}, "NORMAL": {"serverList": ["************:36060", "************:63550", "************:59925", "************:62457", "************:63177"], "maxConnections": 800}}, "readTimeout": 20000, "moduleName": "fs-paas-auth", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:53052"], "maxConnections": 100}}, "readTimeout": 20000, "moduleName": "fs-plat-auth-task", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["*************:13381"], "maxConcurrencyRequests": 400}, "URGENT": {"serverList": [""]}, "GRAY": {"serverList": ["*************:12282"], "maxConcurrencyRequests": 400}, "STAGE": {"serverList": ["*************:19029"], "maxConcurrencyRequests": 400}, "HAOLIYOU": {"serverList": ["************:46205"], "maxConcurrencyRequests": 400}, "YQSL": {"serverList": ["************:41730"], "maxConcurrencyRequests": 400}, "VIP": {"serverList": [""], "maxConnections": 800, "maxConcurrencyRequests": 400}, "NORMAL": {"serverList": ["************:65430", "************:58092", "************:40732", "************:30178", "************:40999"], "maxConnections": 6000, "maxConcurrencyRequests": 400}, "YINLU": {"serverList": ["************:63960"], "maxConcurrencyRequests": 400}, "MEIFU": {"serverList": ["************:12550"], "maxConcurrencyRequests": 400}}, "readTimeout": 20000, "moduleName": "fs-organization-provider", "maxConnections": 8000}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["*************:10717"], "maxConcurrencyRequests": 400}, "GRAY": {"serverList": ["*************:11573"], "maxConcurrencyRequests": 400}, "STAGE": {"serverList": ["*************:11344"], "maxConnections": 6000, "maxConcurrencyRequests": 400}, "NORMAL": {"serverList": ["************:32563"], "maxConnections": 6000, "maxConcurrencyRequests": 400}, "YQSL": {"serverList": ["************:39235"], "maxConcurrencyRequests": 400}, "HAOLIYOU": {"serverList": ["************:44204"], "maxConcurrencyRequests": 400}, "YINLU": {"serverList": ["************:55571"], "maxConcurrencyRequests": 400}, "RUIJIE": {"serverList": ["************:36759"], "maxConcurrencyRequests": 400}}, "readTimeout": 20000, "moduleName": "fs-organization-adapter", "maxConnections": 8000}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:34515"], "maxConnections": 6000}}, "readTimeout": 20000, "moduleName": "fs-organization-adapter-build-expansion", "maxConnections": 8000}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:60969"], "maxConnections": 6000}, "GRAY": {"serverList": ["************:13857"], "maxConnections": 6000}}, "readTimeout": 20000, "moduleName": "fs-plat-org-adapter-provider", "maxConnections": 8000}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:58505"], "maxConnections": 6000}}, "readTimeout": 20000, "moduleName": "fs-organization-sqlserver-proxy-provider", "maxConnections": 5000}, {"poolKeepAliveTime": 30, "environments": {"RUIJIE": {"serverList": ["************:59993"]}, "HAOLIYOU": {"serverList": ["************:43607"]}, "YQSL": {"serverList": ["************:51179"]}, "NORMAL": {"serverList": ["************:52791", "************:38891"], "maxConnections": 800}, "YINLU": {"serverList": ["************:56705"]}, "GRAY": {"serverList": ["************:34346"]}}, "readTimeout": 20000, "moduleName": "fs-organization-provider-4data-auth", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:19664"]}, "haoliyou": {"serverList": ["************:50708"]}, "yqsl": {"serverList": ["************:61487"]}, "yinlu": {"serverList": ["************:42893"]}, "NORMAL": {"serverList": ["paas.nsvc.foneshare.cn"], "maxConnections": 500}}, "readTimeout": 20000, "moduleName": "fs-paas-license", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:10067"], "maxConnections": 800}}, "readTimeout": 30000, "moduleName": "pod", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:54774"], "maxConnections": 800}}, "readTimeout": 30000, "moduleName": "hamster-server", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"JACOCO": {"serverList": ["************:16005"]}, "GRAY": {"serverList": ["************:13013"]}, "NORMAL": {"serverList": ["************:59711"], "maxConnections": 50}}, "readTimeout": 20000, "moduleName": "schedule", "maxConnections": 10}, {"poolKeepAliveTime": 30, "environments": {"URGENT": {"serverList": ["************:14867"]}, "STAGE": {"serverList": ["************:14527"]}, "JACOCO": {"serverList": ["************:19563"]}, "HAOLIYOU": {"serverList": ["************:55196"]}, "YQSL": {"serverList": ["************:55001"]}, "VIP": {"serverList": ["************:51495"]}, "NORMAL": {"serverList": ["************:49712"]}, "YINLU": {"serverList": ["************:40267"]}}, "readTimeout": 20000, "moduleName": "fs-webpage-customer", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"URGENT": {"serverList": ["************:10731"]}, "STAGE": {"serverList": ["************:19352"]}, "JACOCO": {"serverList": ["************:48193"]}, "VIP": {"serverList": ["************:59005"]}, "NORMAL": {"serverList": ["************:51153"]}}, "readTimeout": 20000, "moduleName": "fs-open-app-center-provider", "maxConnections": 100}, {"environments": {"NORMAL": {"serverList": ["************:36717"]}}, "readTimeout": 20000, "moduleName": "fs-qixin-search-message", "maxConnections": 100}, {"environments": {"NORMAL": {"serverList": ["************:43883"]}}, "readTimeout": 20000, "moduleName": "fs-qixin-search", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"URGENT": {"serverList": ["************:15190"]}, "STAGE": {"serverList": ["************:11621 "]}, "JACOCO": {"serverList": ["************:15584 "]}, "HAOLIYOU": {"serverList": ["************:36068"]}, "YQSL": {"serverList": ["************:42679"]}, "NORMAL": {"serverList": ["************:46988"]}, "YINLU": {"serverList": ["************:60728"]}}, "readTimeout": 20000, "moduleName": "fs-user-extension", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"URGENT": {"serverList": [""]}, "GRAY": {"serverList": ["************:18995"]}, "STAGE": {"serverList": [""]}, "VIP": {"serverList": [""], "maxConnections": 50}, "NORMAL": {"serverList": ["************:48632"], "maxConnections": 50}}, "readTimeout": 20000, "moduleName": "component", "maxConnections": 10}, {"poolKeepAliveTime": 30, "environments": {"URGENT": {"serverList": [""]}, "GRAY": {"serverList": ["************:18995"]}, "STAGE": {"serverList": [""]}, "VIP": {"serverList": [""], "maxConnections": 50}, "NORMAL": {"serverList": ["************:48632"], "maxConnections": 50}}, "readTimeout": 20000, "moduleName": "custom_component", "maxConnections": 10}, {"environments": {"URGENT": {"serverList": ["${variables_endpoint.svc_wq_service_host_urgent}"]}, "GRAY": {"serverList": ["${variables_endpoint.svc_wq_service_host_gray}"]}, "STAGE": {"serverList": ["${variables_endpoint.svc_wq_service_host_stage}"]}, "HAOLIYOU": {"serverList": ["${variables_endpoint.svc_wq_service_host_haoliyou}"]}, "YQSL": {"serverList": ["${variables_endpoint.svc_wq_service_host_yqsl}"]}, "VIP": {"serverList": ["${variables_endpoint.svc_wq_service_host_vip}"]}, "SVIP": {"serverList": ["${variables_endpoint.svc_wq_service_host_vip}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_wq_service_host}"]}, "YINLU": {"serverList": ["${variables_endpoint.svc_wq_service_host_yinlu}"]}}, "readTimeout": 15000, "moduleName": "fs-appserver-checkins-v2", "maxConnections": 50}, {"environments": {"ORION": {"serverList": ["************:41923"]}, "GRAY": {"serverList": ["************:17270"]}, "NORMAL": {"serverList": ["************:60178"]}}, "readTimeout": 20000, "moduleName": "checkins-biz", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:11186"]}, "GRAY": {"serverList": ["************:11186"]}}, "readTimeout": 5000, "moduleName": "checkins-office-v2-server", "maxConnections": 50}, {"poolKeepAliveTime": 30, "environments": {"URGENT": {"serverList": ["************:14273"]}, "STAGE": {"serverList": ["************:14896"]}, "YUANQI": {"serverList": ["************:32589"]}, "YINLU": {"serverList": ["************:33642"]}, "HAOLIYOU": {"serverList": ["************:53165"]}, "NORMAL": {"serverList": ["************:62300"]}}, "readTimeout": 8000, "moduleName": "fs-fmcg-sales-cgi", "maxConnections": 50}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_wq_service_host_stage}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_wq_service_host}"]}}, "readTimeout": 15000, "moduleName": "CHECKINS", "maxConnections": 50}, {"environments": {"GRAY": {"serverList": ["${variables_endpoint.svc_wq_task}"]}, "NORMAL": {"serverList": ["${variables_endpoint.svc_wq_task}"]}}, "readTimeout": 15000, "moduleName": "checkins-v2-task", "maxConnections": 50}, {"poolKeepAliveTime": 10, "environments": {"URGENT": {"serverList": ["************:11129"]}, "GRAY": {"serverList": ["*************:15031"]}, "TEST": {"serverList": ["************:55751"]}, "STAGE": {"serverList": ["*************:15854", "*************:11665", "*************:14738"]}, "HAOLIYOU": {"serverList": ["************:41585"]}, "YQSL": {"serverList": ["************:54161"]}, "VIP": {"serverList": ["************:52736"]}, "SVIP": {"serverList": ["************:31287"]}, "NORMAL": {"serverList": ["************:55751", "************:36681", "************:50118", "************:62203", "************:31585"], "maxConnections": 500}, "YINLU": {"serverList": ["************:53061"]}, "MEIFU": {"serverList": ["************:13252"]}, "RUIJIE": {"serverList": ["************:60512"]}}, "readTimeout": 10000, "moduleName": "fs-paas-app-udobj-rest", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"URGENT": {"serverList": ["************:14404"]}, "GRAY": {"serverList": ["*************:13619"]}, "JACOCO": {"serverList": ["*************:16910"]}, "HAOLIYOU": {"serverList": ["************:53630"]}, "STAGE": {"serverList": ["*************:19311", "*************:43392", "*************:53875"]}, "YQSL": {"serverList": ["************:61220"], "maxConnections": 200}, "VIP": {"serverList": ["************:56421"]}, "RUIJIE": {"serverList": ["************:47291"]}, "SVIP": {"serverList": ["************:38432"]}, "MEIFU": {"serverList": ["************:10807"]}, "NORMAL": {"serverList": ["************:57320", "************:59706", "************:51356", "************:51019", "************:61893", "************:13146", "************:10553", "************:15063", "************:12212", "************:12841"]}, "YINLU": {"serverList": ["************:48491"]}}, "readTimeout": 10000, "moduleName": "fs-paas-app-udobj", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["************:13464"]}, "STAGE": {"serverList": ["************:12506"]}, "NORMAL": {"serverList": ["************:17511"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "fs-global-transaction", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"URGENT": {"serverList": ["************:10933"]}, "GRAY": {"serverList": ["************:19601"]}, "JACOCO": {"serverList": ["************:16686"]}, "STAGE": {"serverList": ["************:19156", "************:11048", "************:14580"]}, "HAOLIYOU": {"serverList": ["************:44615"]}, "YQSL": {"serverList": ["************:35623"]}, "YINLU": {"serverList": ["************:37281"]}, "DIDI": {"serverList": ["************:13755"]}, "MEIFU": {"serverList": ["************:13606"]}, "SVIP": {"serverList": ["************:18768"]}, "VIP": {"serverList": ["************:64259"]}, "NORMAL": {"serverList": ["************:12333", "************:15886", "************:14493", "************:13358", "************:12826"]}}, "readTimeout": 10000, "moduleName": "fs-crm-sfa", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["*************:18216"]}, "STAGE": {"serverList": ["*************:17098"]}, "NORMAL": {"serverList": ["************:38606"], "maxConnections": 500}}, "readTimeout": 45000, "moduleName": "fs-paas-metadata-dataloader", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:19349"]}, "NORMAL": {"serverList": ["************:30705"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "fs-message", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:48425"], "maxConnections": 500}}, "readTimeout": 10000, "moduleName": "qixin-provider", "maxConnections": 500}, {"poolKeepAliveTime": 10, "environments": {"NORMAL": {"serverList": ["************:49580"]}}, "readTimeout": 60000, "moduleName": "smartform", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"URGENT": {"serverList": ["************:17637"]}, "STAGE": {"serverList": ["************:11214"]}, "GRAY": {"serverList": ["************:15437"]}, "VIP": {"serverList": ["************:13787"]}, "HAOLIYOU": {"serverList": ["************:15953"]}, "JACOCO": {"serverList": ["************:18573"]}, "NORMAL": {"serverList": ["************:63524"], "maxConnections": 500}}, "readTimeout": 400000, "moduleName": "fs-crm-template", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["************:16353"]}, "haoliyou": {"serverList": ["************:49758"]}, "yqsl": {"serverList": ["************:49072"]}, "VIP": {"serverList": ["************:40091"]}, "yinlu": {"serverList": ["************:49857"]}, "ruijie": {"serverList": ["************:49372"]}, "svip": {"serverList": ["************:57132"]}, "NORMAL": {"serverList": ["************:33209"], "maxConnections": 500}, "jacoco": {"serverList": ["************:14840"], "maxConnections": 500}}, "readTimeout": 20000, "moduleName": "fs-bpm-after-action", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["************:16129"]}, "haoliyou": {"serverList": ["************:48554"]}, "yqsl": {"serverList": ["************:47551"]}, "VIP": {"serverList": ["************:59631"]}, "SVIP": {"serverList": ["************:30898"]}, "yinlu": {"serverList": ["************:46418"]}, "NORMAL": {"serverList": ["************:64636"], "maxConnections": 500}, "jacoco": {"serverList": ["************:18728"], "maxConnections": 500}}, "readTimeout": 120000, "moduleName": "fs-flow", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"NORMAL": {"serverList": ["************:14397"], "maxConnections": 500}, "GRAY": {"serverList": ["************:19676"]}}, "readTimeout": 180000, "moduleName": "fs-flow-orch", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": [], "maxConnections": 20}, "NORMAL": {"serverList": ["************:42283"], "maxConnections": 50}}, "readTimeout": 20000, "moduleName": "fs-qixin-extension-provider", "maxConnections": 10}, {"environments": {"GRAY": {"serverList": ["*************:15119"]}, "NORMAL": {"serverList": ["************:34567"]}}, "readTimeout": 20000, "moduleName": "fs-paas-questionnaire-web", "maxConnections": 100}, {"environments": {"NORMAL": {"serverList": ["************:56932"]}}, "readTimeout": 20000, "moduleName": "fs-support-nps", "maxConnections": 100}, {"environments": {"NORMAL": {"serverList": ["************:56932"]}}, "readTimeout": 20000, "moduleName": "fs-support-feedback", "maxConnections": 100}, {"environments": {"NORMAL": {"serverList": ["************:56932"]}}, "readTimeout": 20000, "moduleName": "fs-support-help", "maxConnections": 100}, {"poolKeepAliveTime": 10, "environments": {"GRAY": {"serverList": ["************:17618"]}, "haoliyou": {"serverList": ["************:45595"]}, "yqsl": {"serverList": ["************:53169"]}, "ruijie": {"serverList": ["************:30108"]}, "vip": {"serverList": ["************:35830"]}, "svip": {"serverList": ["************:45910"]}, "yinlu": {"serverList": ["************:61295"]}, "NORMAL": {"serverList": ["************:34829"], "maxConnections": 500}, "jacoco": {"serverList": ["************:17387"], "maxConnections": 500}}, "readTimeout": 15000, "moduleName": "fs-bpm-biz", "maxConnections": 100}, {"environments": {"JACOCO": {"serverList": ["************:18680"]}, "GRAY": {"serverList": ["************:19009"]}, "STAGE": {"serverList": ["************:13540"]}, "VIP": {"serverList": ["************:65000"]}, "SVIP": {"serverList": ["************:12607"]}, "NORMAL": {"serverList": ["************:35216"]}}, "readTimeout": 15000, "moduleName": "fs-schedule-task", "maxConnections": 100}, {"poolKeepAliveTime": 30, "environments": {"HAOLIYOU": {"serverList": ["************:33571"]}, "YQSL": {"serverList": ["************:58561"]}, "NORMAL": {"serverList": ["************:41838"]}, "YINLU": {"serverList": ["************:48330"]}, "GRAY": {"serverList": ["************:14794"]}, "STAGE": {"serverList": ["************:14191"]}}, "readTimeout": 30000, "moduleName": "fs-metadata-rest", "maxConnections": 1000}, {"poolKeepAliveTime": 30, "environments": {"VIP": {"serverList": ["************:18804"]}, "NORMAL": {"serverList": ["************:43307"]}}, "readTimeout": 30000, "moduleName": "bizconf", "maxConnections": 1000}, {"poolKeepAliveTime": 30, "environments": {"VIP": {"serverList": ["************:18281"]}, "NORMAL": {"serverList": ["************:33613"]}}, "readTimeout": 30000, "moduleName": "metadata-option", "maxConnections": 1000}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:48413"]}, "haoliyou": {"serverList": ["************:35107"]}, "STAGE": {"serverList": ["************:17315"]}, "SVIP": {"serverList": ["************:59610"]}, "URGENT": {"serverList": ["************:11491"]}, "VIP": {"serverList": ["************:56046"]}, "yinlu": {"serverList": ["************:41981"]}, "yqsl": {"serverList": ["************:41981"]}}, "readTimeout": 10000, "moduleName": "fs-crm", "maxConnections": 200}, {"poolKeepAliveTime": 30, "environments": {"NORMAL": {"serverList": ["************:56196"]}, "YQSL": {"serverList": ["************:52608"]}, "URGENT": {"serverList": ["************:18201"]}, "STAGE": {"serverList": ["************:10139"]}}, "readTimeout": 20000, "moduleName": "fs-sail-order", "maxConnections": 500}, {"poolKeepAliveTime": 30, "environments": {"GRAY": {"serverList": ["************:15975"]}, "NORMAL": {"serverList": ["************:60419"], "maxConnections": 50}}, "readTimeout": 20000, "moduleName": "i18n-setting", "maxConnections": 10}], "crossCloud": [{"cloudId": "kehua-public-test", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_kehua_test_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_kehua_test}"}, {"cloudId": "kehua-public-prod", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_kehua_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_kehua_public}"}, {"cloudId": "cpgc-public-prod", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_cpgc_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_cpgc_public}"}, {"cloudId": "wingd-public-prod", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_wingd_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_wingd_public}"}, {"cloudId": "huaweicloud-public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_huaweicloud_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_huaweicloud_public}"}, {"cloudId": "huaweicloud_sbt", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_huaweicloud_sbt_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_huaweicloud_sbt}"}, {"cloudId": "ucd-public-test", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_ucd_public_test_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_ucd_public_test}"}, {"cloudId": "ucd-public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_ucd_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_ucd_public}"}, {"cloudId": "ksc_ksc", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_ksc_ksc_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_ksc_ksc}"}, {"cloudId": "hws_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_hws_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_hws_public}"}, {"cloudId": "ale_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_ale_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_ale_public}"}, {"cloudId": "haixin_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_haixin_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_haixin_public}"}, {"cloudId": "xuji_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_xuji_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_xuji_public}"}, {"cloudId": "forceecrm_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_forceecrm_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_forceecrm_public}"}, {"cloudId": "mengniu_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_mengniu_public_global}"], "maxConnections": 100, "routeTenants": "${variables_apibus.cross_tenants_mengniu_public}"}, {"cloudId": "hsyk_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_hsyk_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_hsyk_public}"}, {"cloudId": "chinatower_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_chinatower_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_chinatower_public}"}, {"cloudId": "yangnongchem_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_yangnongchem_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_yangnongchem_public}"}, {"cloudId": "wuzizui99_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_wuzizui99_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_wuzizui99_public}"}, {"cloudId": "hexagonmi_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_hexagonmi_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_hexagonmi_public}"}, {"cloudId": "iflytek_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_iflytek_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_iflytek_public}"}, {"cloudId": "cloudmodel_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_cloudmodel_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_cloudmodel_public}"}, {"cloudId": "kemaicrm_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_kemaicrm_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_kemaicrm_public}"}, {"cloudId": "teleagi_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_teleagi_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_teleagi_public}"}, {"cloudId": "forsharecrm_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_forsharecrm_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_forsharecrm_public}"}, {"cloudId": "allink8s_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_allink8s_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_allink8s_public}"}, {"cloudId": "jingbo_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_jingbo_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_jingbo_public}"}, {"cloudId": "tbea_public", "readTimeout": 20000, "serverList": ["${variables_apibus.cross_nginx_tbea_public_global}"], "maxConnections": 10, "routeTenants": "${variables_apibus.cross_tenants_tbea_public}"}]}