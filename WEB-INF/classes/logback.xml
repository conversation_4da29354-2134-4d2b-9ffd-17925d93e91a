<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/api-bus.log</file>
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/api-bus-%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%thread] %-5level %logger{12} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                org.codehaus.groovy,
                groovy.lang,
                java.lang.reflect,
                }%n
            </pattern>
        </encoder>
    </appender>

    <appender name="ACCESS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/access.log</file>
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/access-%d{yyyy-MM-dd.HH}.%i.log</fileNamePattern>
            <maxHistory>48</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>30GB</totalSizeCap>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%thread] %-5level %logger{12} %msg%rEx{full}%n</pattern>
        </encoder>
    </appender>

    <appender name="SlowOrError" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/slowOrError-access.log</file>
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/slowOrError-access-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>30GB</totalSizeCap>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%thread] %-5level %logger{12} %msg%rEx{full}%n</pattern>
        </encoder>
    </appender>

    <appender name="CrossCloud" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/crossCloud-access.log</file>
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/crossCloud-access-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>30GB</totalSizeCap>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%thread] %-5level %logger{12} %msg%rEx{full}%n</pattern>
        </encoder>
    </appender>


    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                org.codehaus.groovy,
                }%n
            </pattern>
        </encoder>
    </appender>

    <!-- 异步输出日志避免阻塞服务 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="RollingFile"/>
    </appender>

    <appender name="ACCESS-ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="ACCESS"/>
    </appender>

    <appender name="SlowOrError-ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="SlowOrError"/>
    </appender>

    <appender name="CrossCloud-ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="CrossCloud"/>
    </appender>

    <!-- 配置基础组件为WARN级别，避免打印过多影响服务自己日志 -->
    <logger name="druid.sql" level="INFO"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>

    <logger name="zuul.server.nettylog" level="WARN"/>
    <logger name="zuul.origin.nettylog" level="WARN"/>
    <logger name="com.netflix.loadbalancer" level="WARN"/>
    <logger name="com.netflix.config" level="WARN"/>
    <logger name="com.facishare.api.handler" level="INFO"/>

    <logger name="ACCESS" level="INFO" additivity="false">
        <appender-ref ref="ACCESS-ASYNC"/>
    </logger>

    <logger name="SlowOrError" level="INFO" additivity="false">
        <appender-ref ref="SlowOrError-ASYNC"/>
    </logger>

    <logger name="CrossCloud" level="INFO" additivity="false">
        <appender-ref ref="CrossCloud-ASYNC"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="ASYNC"/>
    </root>
</configuration>
