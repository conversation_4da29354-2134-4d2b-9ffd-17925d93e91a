<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-paas-api-bus</artifactId>
        <groupId>com.facishare</groupId>
        <version>1.2.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <version>1.2.0-SNAPSHOT</version>
    <artifactId>fs-apibus-global</artifactId>

    <packaging>war</packaging>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-apibus-core</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-circuit-breaker</artifactId>
                    <groupId>com.fxiaoke.circuit.breaker</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.circuit.breaker</groupId>
            <artifactId>fs-circuit-breaker</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-servlet-api</artifactId>
            <version>7.0.76</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>